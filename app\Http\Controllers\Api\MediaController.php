<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Media;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class MediaController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Media::query();

        // Filter by file type
        if ($request->has('type')) {
            $query->where('file_type', $request->type);
        }

        // Filter by featured
        if ($request->has('featured')) {
            $query->where('is_featured', $request->boolean('featured'));
        }

        $media = $query->ordered()->paginate(12);

        return response()->json($media);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'files.*' => 'required|file|mimes:jpeg,png,jpg,gif,mp4,mov,avi,pdf,doc,docx|max:20480', // 20MB max
            'alt_text' => 'nullable|string|max:255',
            'description' => 'nullable|string',
        ]);

        $uploadedFiles = [];

        if ($request->hasFile('files')) {
            foreach ($request->file('files') as $file) {
                $originalName = $file->getClientOriginalName();
                $fileName = time() . '_' . uniqid() . '.' . $file->getClientOriginalExtension();
                $filePath = $file->storeAs('media', $fileName, 'public');

                // Determine file type
                $mimeType = $file->getMimeType();
                $fileType = 'document';
                if (str_starts_with($mimeType, 'image/')) {
                    $fileType = 'image';
                } elseif (str_starts_with($mimeType, 'video/')) {
                    $fileType = 'video';
                }

                // Get file metadata
                $metadata = [
                    'original_name' => $originalName,
                ];

                if ($fileType === 'image') {
                    $imageInfo = getimagesize($file->getPathname());
                    if ($imageInfo) {
                        $metadata['width'] = $imageInfo[0];
                        $metadata['height'] = $imageInfo[1];
                    }
                }

                $media = Media::create([
                    'title' => pathinfo($originalName, PATHINFO_FILENAME),
                    'description' => $request->description,
                    'file_path' => $filePath,
                    'file_name' => $fileName,
                    'file_type' => $fileType,
                    'mime_type' => $mimeType,
                    'file_size' => $file->getSize(),
                    'alt_text' => $request->alt_text,
                    'metadata' => $metadata,
                    'is_featured' => false,
                    'original_name' => $originalName,
                ]);

                $uploadedFiles[] = $media;
            }
        }

        return response()->json([
            'success' => true,
            'message' => count($uploadedFiles) . ' file(s) uploaded successfully',
            'data' => $uploadedFiles
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(Media $media)
    {
        return response()->json($media);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Media $media)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'alt_text' => 'nullable|string|max:255',
            'is_featured' => 'boolean',
        ]);

        $media->update([
            'title' => $request->title,
            'description' => $request->description,
            'alt_text' => $request->alt_text,
            'is_featured' => $request->boolean('is_featured'),
        ]);

        return response()->json($media);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Media $media)
    {
        // Delete the file from storage
        if (Storage::disk('public')->exists($media->file_path)) {
            Storage::disk('public')->delete($media->file_path);
        }

        $media->delete();

        return response()->json([
            'message' => 'Media deleted successfully'
        ]);
    }
}
