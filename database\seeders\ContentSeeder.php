<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Content;

class ContentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $contents = [
            // Homepage Hero Section
            [
                'key' => 'homepage_hero_title',
                'title' => 'Hero Section Title',
                'content' => 'Building Your Dreams with Excellence',
                'type' => 'text',
                'page' => 'homepage',
                'section' => 'hero',
                'description' => 'Main headline displayed in the hero section',
                'sort_order' => 1,
                'is_active' => true,
            ],
            [
                'key' => 'homepage_hero_subtitle',
                'title' => 'Hero Section Subtitle',
                'content' => 'We are a trusted construction company with over 20 years of experience in delivering quality residential and commercial projects. From concept to completion, we build with precision and care.',
                'type' => 'textarea',
                'page' => 'homepage',
                'section' => 'hero',
                'description' => 'Subtitle text displayed below the main headline',
                'sort_order' => 2,
                'is_active' => true,
            ],
            [
                'key' => 'homepage_hero_image',
                'title' => 'Hero Section Image',
                'content' => 'https://images.unsplash.com/photo-1541888946425-d81bb19240f5?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
                'type' => 'image',
                'page' => 'homepage',
                'section' => 'hero',
                'description' => 'Main image displayed in the hero section',
                'sort_order' => 3,
                'is_active' => true,
            ],

            // Homepage Services Section
            [
                'key' => 'homepage_services_title',
                'title' => 'Services Section Title',
                'content' => 'Our Services',
                'type' => 'text',
                'page' => 'homepage',
                'section' => 'services',
                'description' => 'Title for the services section',
                'sort_order' => 1,
                'is_active' => true,
            ],
            [
                'key' => 'homepage_services_subtitle',
                'title' => 'Services Section Subtitle',
                'content' => 'We offer comprehensive construction services to meet all your building needs',
                'type' => 'text',
                'page' => 'homepage',
                'section' => 'services',
                'description' => 'Subtitle for the services section',
                'sort_order' => 2,
                'is_active' => true,
            ],

            // Homepage Projects Section
            [
                'key' => 'homepage_projects_title',
                'title' => 'Projects Section Title',
                'content' => 'Featured Projects',
                'type' => 'text',
                'page' => 'homepage',
                'section' => 'projects',
                'description' => 'Title for the featured projects section',
                'sort_order' => 1,
                'is_active' => true,
            ],
            [
                'key' => 'homepage_projects_subtitle',
                'title' => 'Projects Section Subtitle',
                'content' => 'Take a look at some of our recent completed projects',
                'type' => 'text',
                'page' => 'homepage',
                'section' => 'projects',
                'description' => 'Subtitle for the featured projects section',
                'sort_order' => 2,
                'is_active' => true,
            ],

            // Homepage Why Choose Us Section
            [
                'key' => 'homepage_why_choose_title',
                'title' => 'Why Choose Us Title',
                'content' => 'Why Choose Us',
                'type' => 'text',
                'page' => 'homepage',
                'section' => 'why_choose',
                'description' => 'Title for the why choose us section',
                'sort_order' => 1,
                'is_active' => true,
            ],
            [
                'key' => 'homepage_why_choose_subtitle',
                'title' => 'Why Choose Us Subtitle',
                'content' => 'We are committed to delivering exceptional construction services',
                'type' => 'text',
                'page' => 'homepage',
                'section' => 'why_choose',
                'description' => 'Subtitle for the why choose us section',
                'sort_order' => 2,
                'is_active' => true,
            ],

            // Homepage CTA Section
            [
                'key' => 'homepage_cta_title',
                'title' => 'Call to Action Title',
                'content' => 'Ready to Start Your Project?',
                'type' => 'text',
                'page' => 'homepage',
                'section' => 'cta',
                'description' => 'Title for the call to action section',
                'sort_order' => 1,
                'is_active' => true,
            ],
            [
                'key' => 'homepage_cta_subtitle',
                'title' => 'Call to Action Subtitle',
                'content' => 'Contact us today for a free consultation and quote. Let\'s bring your construction vision to life.',
                'type' => 'text',
                'page' => 'homepage',
                'section' => 'cta',
                'description' => 'Subtitle for the call to action section',
                'sort_order' => 2,
                'is_active' => true,
            ],

            // About Page Content
            [
                'key' => 'about_hero_title',
                'title' => 'About Hero Title',
                'content' => 'About ConstructCo',
                'type' => 'text',
                'page' => 'about',
                'section' => 'hero',
                'description' => 'Main title for the about page hero section',
                'sort_order' => 1,
                'is_active' => true,
            ],
            [
                'key' => 'about_hero_subtitle',
                'title' => 'About Hero Subtitle',
                'content' => 'With over 20 years of experience in the construction industry, we have built a reputation for excellence, reliability, and innovation in every project we undertake.',
                'type' => 'textarea',
                'page' => 'about',
                'section' => 'hero',
                'description' => 'Subtitle for the about page hero section',
                'sort_order' => 2,
                'is_active' => true,
            ],

            // About Story Section
            [
                'key' => 'about_story_title',
                'title' => 'Our Story Title',
                'content' => 'Our Story',
                'type' => 'text',
                'page' => 'about',
                'section' => 'story',
                'description' => 'Title for the our story section',
                'sort_order' => 1,
                'is_active' => true,
            ],
            [
                'key' => 'about_story_subtitle',
                'title' => 'Our Story Subtitle',
                'content' => 'From humble beginnings to industry leaders',
                'type' => 'text',
                'page' => 'about',
                'section' => 'story',
                'description' => 'Subtitle for the our story section',
                'sort_order' => 2,
                'is_active' => true,
            ],
            [
                'key' => 'about_story_content',
                'title' => 'Our Story Content',
                'content' => 'ConstructCo was founded with a simple mission: to provide exceptional construction services that exceed our clients\' expectations. What started as a small family business has grown into one of the region\'s most trusted construction companies.',
                'type' => 'html',
                'page' => 'about',
                'section' => 'story',
                'description' => 'Main content for the our story section',
                'sort_order' => 3,
                'is_active' => true,
            ],

            // About Mission Section
            [
                'key' => 'about_mission_title',
                'title' => 'Mission & Values Title',
                'content' => 'Our Mission & Values',
                'type' => 'text',
                'page' => 'about',
                'section' => 'mission',
                'description' => 'Title for the mission and values section',
                'sort_order' => 1,
                'is_active' => true,
            ],
            [
                'key' => 'about_mission_subtitle',
                'title' => 'Mission & Values Subtitle',
                'content' => 'The principles that guide everything we do',
                'type' => 'text',
                'page' => 'about',
                'section' => 'mission',
                'description' => 'Subtitle for the mission and values section',
                'sort_order' => 2,
                'is_active' => true,
            ],

            // Company Information
            [
                'key' => 'company_name',
                'title' => 'Company Name',
                'content' => 'ConstructCo',
                'type' => 'text',
                'page' => 'general',
                'section' => 'company',
                'description' => 'Official company name',
                'sort_order' => 1,
                'is_active' => true,
            ],
            [
                'key' => 'company_phone',
                'title' => 'Company Phone',
                'content' => '(*************',
                'type' => 'text',
                'page' => 'general',
                'section' => 'contact',
                'description' => 'Main company phone number',
                'sort_order' => 1,
                'is_active' => true,
            ],
            [
                'key' => 'company_email',
                'title' => 'Company Email',
                'content' => '<EMAIL>',
                'type' => 'text',
                'page' => 'general',
                'section' => 'contact',
                'description' => 'Main company email address',
                'sort_order' => 2,
                'is_active' => true,
            ],
            [
                'key' => 'company_address',
                'title' => 'Company Address',
                'content' => '123 Construction St, City, State 12345',
                'type' => 'text',
                'page' => 'general',
                'section' => 'contact',
                'description' => 'Company physical address',
                'sort_order' => 3,
                'is_active' => true,
            ],
        ];

        foreach ($contents as $content) {
            Content::updateOrCreate(
                ['key' => $content['key']],
                $content
            );
        }
    }
}
