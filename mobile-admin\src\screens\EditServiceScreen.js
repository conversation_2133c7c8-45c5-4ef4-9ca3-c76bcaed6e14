import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
  Image,
  Switch,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import { apiService } from '../services/apiService';

const EditServiceScreen = ({ route, navigation }) => {
  const { serviceId } = route.params;
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [service, setService] = useState({
    title: '',
    short_description: '',
    description: '',
    price_from: '',
    icon: '',
    sort_order: 0,
    is_active: true,
    is_featured: false,
    image: null,
  });
  const [newImage, setNewImage] = useState(null);

  useEffect(() => {
    loadService();
  }, []);

  const loadService = async () => {
    try {
      const response = await apiService.getServiceForEdit(serviceId);
      setService(response);
    } catch (error) {
      console.error('Error loading service:', error);
      Alert.alert('Error', 'Failed to load service details');
      navigation.goBack();
    } finally {
      setLoading(false);
    }
  };

  const pickImage = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        setNewImage(result.assets[0]);
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Error', 'Failed to pick image');
    }
  };

  const removeNewImage = () => {
    setNewImage(null);
  };

  const removeExistingImage = () => {
    setService({ ...service, image: null });
  };

  const handleSave = async () => {
    if (!service.title.trim()) {
      Alert.alert('Error', 'Please enter a service title');
      return;
    }

    setSaving(true);
    try {
      const formData = new FormData();

      // Add service data
      formData.append('title', service.title);
      formData.append('short_description', service.short_description);
      formData.append('description', service.description);
      formData.append('price_from', service.price_from);
      formData.append('icon', service.icon);
      formData.append('sort_order', service.sort_order.toString());
      formData.append('is_active', service.is_active ? '1' : '0');
      formData.append('is_featured', service.is_featured ? '1' : '0');

      // Add new image if selected
      if (newImage) {
        formData.append('image', {
          uri: newImage.uri,
          type: 'image/jpeg',
          name: 'service_image.jpg',
        });
      }

      await apiService.updateService(serviceId, formData);

      Alert.alert('Success', 'Service updated successfully', [
        { text: 'OK', onPress: () => navigation.goBack() }
      ]);
    } catch (error) {
      console.error('Error updating service:', error);
      Alert.alert('Error', 'Failed to update service');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#FFB703" />
        <Text style={styles.loadingText}>Loading service...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color="#023047" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Edit Service</Text>
        <TouchableOpacity onPress={handleSave} disabled={saving}>
          {saving ? (
            <ActivityIndicator size="small" color="#FFB703" />
          ) : (
            <Ionicons name="checkmark" size={24} color="#FFB703" />
          )}
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.form}>
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Service Title *</Text>
            <TextInput
              style={styles.input}
              value={service.title}
              onChangeText={(text) => setService({ ...service, title: text })}
              placeholder="Enter service title"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Short Description</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={service.short_description}
              onChangeText={(text) => setService({ ...service, short_description: text })}
              placeholder="Brief description for cards"
              multiline
              numberOfLines={2}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Full Description</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={service.description}
              onChangeText={(text) => setService({ ...service, description: text })}
              placeholder="Detailed service description"
              multiline
              numberOfLines={4}
            />
          </View>

          <View style={styles.row}>
            <View style={[styles.inputGroup, styles.halfWidth]}>
              <Text style={styles.label}>Price From</Text>
              <TextInput
                style={styles.input}
                value={service.price_from}
                onChangeText={(text) => setService({ ...service, price_from: text })}
                placeholder="Starting price"
                keyboardType="numeric"
              />
            </View>

            <View style={[styles.inputGroup, styles.halfWidth]}>
              <Text style={styles.label}>Sort Order</Text>
              <TextInput
                style={styles.input}
                value={service.sort_order.toString()}
                onChangeText={(text) => setService({ ...service, sort_order: parseInt(text) || 0 })}
                placeholder="Display order"
                keyboardType="numeric"
              />
            </View>
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Icon Class</Text>
            <TextInput
              style={styles.input}
              value={service.icon}
              onChangeText={(text) => setService({ ...service, icon: text })}
              placeholder="e.g., fas fa-hammer"
            />
          </View>

          {/* Current Image */}
          {service.image && !newImage && (
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Current Image</Text>
              <View style={styles.imageContainer}>
                <Image
                  source={{ uri: `http://192.168.0.3:8000/storage/${service.image}` }}
                  style={styles.currentImage}
                />
                <TouchableOpacity
                  style={styles.removeImageButton}
                  onPress={removeExistingImage}
                >
                  <Ionicons name="close" size={16} color="#fff" />
                </TouchableOpacity>
              </View>
            </View>
          )}

          {/* New Image */}
          {newImage && (
            <View style={styles.inputGroup}>
              <Text style={styles.label}>New Image</Text>
              <View style={styles.imageContainer}>
                <Image source={{ uri: newImage.uri }} style={styles.currentImage} />
                <TouchableOpacity
                  style={styles.removeImageButton}
                  onPress={removeNewImage}
                >
                  <Ionicons name="close" size={16} color="#fff" />
                </TouchableOpacity>
              </View>
            </View>
          )}

          <TouchableOpacity style={styles.imagePickerButton} onPress={pickImage}>
            <Ionicons name="camera" size={20} color="#FFB703" />
            <Text style={styles.imagePickerText}>
              {service.image || newImage ? 'Change Image' : 'Add Image'}
            </Text>
          </TouchableOpacity>

          {/* Switches */}
          <View style={styles.switchContainer}>
            <View style={styles.switchRow}>
              <Text style={styles.switchLabel}>Active</Text>
              <Switch
                value={service.is_active}
                onValueChange={(value) => setService({ ...service, is_active: value })}
                trackColor={{ false: '#767577', true: '#FFB703' }}
                thumbColor={service.is_active ? '#fff' : '#f4f3f4'}
              />
            </View>

            <View style={styles.switchRow}>
              <Text style={styles.switchLabel}>Featured</Text>
              <Switch
                value={service.is_featured}
                onValueChange={(value) => setService({ ...service, is_featured: value })}
                trackColor={{ false: '#767577', true: '#FFB703' }}
                thumbColor={service.is_featured ? '#fff' : '#f4f3f4'}
              />
            </View>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#023047',
  },
  content: {
    flex: 1,
  },
  form: {
    padding: 20,
  },
  inputGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#023047',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#fff',
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  halfWidth: {
    width: '48%',
  },
  imageContainer: {
    position: 'relative',
    alignSelf: 'flex-start',
  },
  currentImage: {
    width: 150,
    height: 100,
    borderRadius: 8,
  },
  removeImageButton: {
    position: 'absolute',
    top: -5,
    right: -5,
    backgroundColor: '#FF6B6B',
    borderRadius: 10,
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  imagePickerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: '#FFB703',
    borderStyle: 'dashed',
    borderRadius: 8,
    padding: 20,
    marginBottom: 20,
  },
  imagePickerText: {
    marginLeft: 8,
    fontSize: 16,
    color: '#FFB703',
    fontWeight: '600',
  },
  switchContainer: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 15,
  },
  switchRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 10,
  },
  switchLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#023047',
  },
});

export default EditServiceScreen;
