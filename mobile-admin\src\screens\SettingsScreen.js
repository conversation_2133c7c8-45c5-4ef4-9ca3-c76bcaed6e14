import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
  TextInput,
  Switch,
  Modal,
} from 'react-native';
import { Picker } from '@react-native-picker/picker';
import apiService from '../services/apiService';

const SettingsScreen = ({ navigation }) => {
  const [settings, setSettings] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedGroup, setSelectedGroup] = useState('general');
  const [groups, setGroups] = useState([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [formData, setFormData] = useState({
    key: '',
    value: '',
    type: 'text',
    group: 'general',
    description: '',
  });

  useEffect(() => {
    loadSettings();
  }, [selectedGroup]);

  const loadSettings = async () => {
    try {
      setLoading(true);
      const params = selectedGroup !== 'all' ? { group: selectedGroup } : {};
      const response = await apiService.getSettings(params);

      if (response.data && response.data.settings) {
        setSettings(response.data.settings);
        setGroups(response.data.groups || []);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to load settings');
      console.error('Error loading settings:', error);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadSettings();
    setRefreshing(false);
  };

  const handleSaveAll = async () => {
    try {
      const settingsData = {};
      settings.forEach(setting => {
        settingsData[setting.key] = setting.value;
      });

      await apiService.updateSettings({ settings: settingsData });
      Alert.alert('Success', 'Settings saved successfully');
    } catch (error) {
      Alert.alert('Error', 'Failed to save settings');
      console.error('Error saving settings:', error);
    }
  };

  const handleAddSetting = async () => {
    try {
      await apiService.createSetting(formData);
      Alert.alert('Success', 'Setting added successfully');
      setModalVisible(false);
      setFormData({
        key: '',
        value: '',
        type: 'text',
        group: 'general',
        description: '',
      });
      loadSettings();
    } catch (error) {
      Alert.alert('Error', 'Failed to add setting');
      console.error('Error adding setting:', error);
    }
  };

  const handleDeleteSetting = (setting) => {
    Alert.alert(
      'Delete Setting',
      `Are you sure you want to delete "${setting.key}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await apiService.deleteSetting(setting.id);
              Alert.alert('Success', 'Setting deleted successfully');
              loadSettings();
            } catch (error) {
              Alert.alert('Error', 'Failed to delete setting');
            }
          },
        },
      ]
    );
  };

  const updateSettingValue = (index, value) => {
    const updatedSettings = [...settings];
    updatedSettings[index].value = value;
    setSettings(updatedSettings);
  };

  const renderSettingInput = (setting, index) => {
    switch (setting.type) {
      case 'boolean':
        return (
          <View style={styles.switchContainer}>
            <Switch
              value={setting.value === '1' || setting.value === true}
              onValueChange={(value) => updateSettingValue(index, value ? '1' : '0')}
              trackColor={{ false: '#767577', true: '#007bff' }}
              thumbColor={setting.value ? '#fff' : '#f4f3f4'}
            />
            <Text style={styles.switchLabel}>
              {setting.value === '1' || setting.value === true ? 'Enabled' : 'Disabled'}
            </Text>
          </View>
        );
      case 'textarea':
        return (
          <TextInput
            style={[styles.input, styles.textArea]}
            value={setting.value || ''}
            onChangeText={(text) => updateSettingValue(index, text)}
            multiline
            numberOfLines={3}
            placeholder="Enter value..."
          />
        );
      case 'integer':
        return (
          <TextInput
            style={styles.input}
            value={setting.value || ''}
            onChangeText={(text) => updateSettingValue(index, text)}
            keyboardType="numeric"
            placeholder="Enter number..."
          />
        );
      case 'float':
        return (
          <TextInput
            style={styles.input}
            value={setting.value || ''}
            onChangeText={(text) => updateSettingValue(index, text)}
            keyboardType="decimal-pad"
            placeholder="Enter decimal number..."
          />
        );
      default:
        return (
          <TextInput
            style={styles.input}
            value={setting.value || ''}
            onChangeText={(text) => updateSettingValue(index, text)}
            placeholder="Enter value..."
          />
        );
    }
  };

  const groupedSettings = settings.reduce((acc, setting) => {
    const group = setting.group || 'general';
    if (!acc[group]) {
      acc[group] = [];
    }
    acc[group].push(setting);
    return acc;
  }, {});

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Settings</Text>
        <View style={styles.headerButtons}>
          <TouchableOpacity style={styles.addButton} onPress={() => setModalVisible(true)}>
            <Text style={styles.addButtonText}>+ Add</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.saveButton} onPress={handleSaveAll}>
            <Text style={styles.saveButtonText}>Save All</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Group Filter */}
      <View style={styles.filterContainer}>
        <View style={styles.pickerContainer}>
          <Picker
            selectedValue={selectedGroup}
            onValueChange={setSelectedGroup}
            style={styles.picker}
          >
            <Picker.Item label="All Groups" value="all" />
            {groups.map((group) => (
              <Picker.Item
                key={group}
                label={group.charAt(0).toUpperCase() + group.slice(1)}
                value={group}
              />
            ))}
          </Picker>
        </View>
      </View>

      {/* Settings List */}
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {Object.entries(groupedSettings).map(([groupName, groupSettings]) => (
          <View key={groupName} style={styles.settingsGroup}>
            <Text style={styles.groupTitle}>
              {groupName.charAt(0).toUpperCase() + groupName.slice(1)} Settings
            </Text>

            {groupSettings.map((setting, index) => {
              const globalIndex = settings.findIndex(s => s.id === setting.id);
              return (
                <View key={setting.id} style={styles.settingItem}>
                  <View style={styles.settingHeader}>
                    <View style={styles.settingInfo}>
                      <Text style={styles.settingTitle}>
                        {setting.key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </Text>
                      {setting.description && (
                        <Text style={styles.settingDescription}>{setting.description}</Text>
                      )}
                      <Text style={styles.settingKey}>Key: {setting.key}</Text>
                    </View>
                    <TouchableOpacity
                      style={styles.deleteButton}
                      onPress={() => handleDeleteSetting(setting)}
                    >
                      <Text style={styles.deleteButtonText}>×</Text>
                    </TouchableOpacity>
                  </View>

                  <View style={styles.settingInput}>
                    {renderSettingInput(setting, globalIndex)}
                  </View>

                  <View style={styles.settingMeta}>
                    <Text style={styles.settingType}>Type: {setting.type}</Text>
                  </View>
                </View>
              );
            })}
          </View>
        ))}
      </ScrollView>

      {/* Add Setting Modal */}
      <Modal
        visible={modalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setModalVisible(false)}>
              <Text style={styles.modalCancelText}>Cancel</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Add Setting</Text>
            <TouchableOpacity onPress={handleAddSetting}>
              <Text style={styles.modalSaveText}>Add</Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent}>
            <View style={styles.formGroup}>
              <Text style={styles.label}>Key *</Text>
              <TextInput
                style={styles.input}
                value={formData.key}
                onChangeText={(text) => setFormData({ ...formData, key: text })}
                placeholder="e.g., site_name"
              />
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Value</Text>
              <TextInput
                style={styles.input}
                value={formData.value}
                onChangeText={(text) => setFormData({ ...formData, value: text })}
                placeholder="Enter value"
              />
            </View>

            <View style={styles.formRow}>
              <View style={styles.formGroupHalf}>
                <Text style={styles.label}>Type *</Text>
                <View style={styles.pickerContainer}>
                  <Picker
                    selectedValue={formData.type}
                    onValueChange={(value) => setFormData({ ...formData, type: value })}
                    style={styles.picker}
                  >
                    <Picker.Item label="Text" value="text" />
                    <Picker.Item label="Textarea" value="textarea" />
                    <Picker.Item label="Boolean" value="boolean" />
                    <Picker.Item label="Integer" value="integer" />
                    <Picker.Item label="Float" value="float" />
                    <Picker.Item label="JSON" value="json" />
                    <Picker.Item label="Image" value="image" />
                  </Picker>
                </View>
              </View>

              <View style={styles.formGroupHalf}>
                <Text style={styles.label}>Group *</Text>
                <View style={styles.pickerContainer}>
                  <Picker
                    selectedValue={formData.group}
                    onValueChange={(value) => setFormData({ ...formData, group: value })}
                    style={styles.picker}
                  >
                    <Picker.Item label="General" value="general" />
                    <Picker.Item label="Website" value="website" />
                    <Picker.Item label="Email" value="email" />
                    <Picker.Item label="Security" value="security" />
                    <Picker.Item label="Appearance" value="appearance" />
                    <Picker.Item label="Contact" value="contact" />
                  </Picker>
                </View>
              </View>
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Description</Text>
              <TextInput
                style={[styles.input, styles.textArea]}
                value={formData.description}
                onChangeText={(text) => setFormData({ ...formData, description: text })}
                placeholder="Help text for administrators"
                multiline
                numberOfLines={3}
              />
            </View>
          </ScrollView>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  headerButtons: {
    flexDirection: 'row',
  },
  addButton: {
    backgroundColor: '#007bff',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    marginRight: 8,
  },
  addButtonText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 14,
  },
  saveButton: {
    backgroundColor: '#28a745',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  saveButtonText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 14,
  },
  filterContainer: {
    backgroundColor: '#fff',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 6,
    backgroundColor: '#fff',
  },
  picker: {
    height: 40,
  },
  scrollView: {
    flex: 1,
  },
  settingsGroup: {
    backgroundColor: '#fff',
    margin: 8,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  groupTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#007bff',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  settingItem: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  settingHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  settingInfo: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  settingDescription: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  settingKey: {
    fontSize: 10,
    color: '#999',
    fontFamily: 'monospace',
  },
  deleteButton: {
    backgroundColor: '#dc3545',
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  deleteButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  settingInput: {
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 6,
    paddingHorizontal: 12,
    paddingVertical: 8,
    backgroundColor: '#fff',
    fontSize: 14,
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  switchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  switchLabel: {
    marginLeft: 8,
    fontSize: 14,
    color: '#333',
  },
  settingMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  settingType: {
    fontSize: 10,
    color: '#999',
    fontStyle: 'italic',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#fff',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  modalCancelText: {
    color: '#007bff',
    fontSize: 16,
  },
  modalSaveText: {
    color: '#007bff',
    fontSize: 16,
    fontWeight: '600',
  },
  modalContent: {
    flex: 1,
    padding: 16,
  },
  formGroup: {
    marginBottom: 16,
  },
  formRow: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  formGroupHalf: {
    flex: 1,
    marginRight: 8,
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
});

export default SettingsScreen;
