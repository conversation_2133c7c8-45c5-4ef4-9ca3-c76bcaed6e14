import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  StatusBar,
  Platform,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { Ionicons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import theme from '../../theme';

const Header = ({
  title,
  subtitle,
  leftIcon = 'arrow-back',
  rightIcon,
  onLeftPress,
  onRightPress,
  variant = 'default',
  gradient = false,
  blur = false,
  transparent = false,
  centerComponent,
  rightComponent,
  style,
  titleStyle,
  ...props
}) => {
  const insets = useSafeAreaInsets();
  
  const getHeaderStyle = () => {
    const baseStyle = [
      styles.header,
      { paddingTop: insets.top + theme.spacing.sm }
    ];
    
    switch (variant) {
      case 'large':
        baseStyle.push(styles.headerLarge);
        break;
      case 'compact':
        baseStyle.push(styles.headerCompact);
        break;
      default:
        baseStyle.push(styles.headerDefault);
    }
    
    if (transparent) {
      baseStyle.push(styles.headerTransparent);
    } else {
      baseStyle.push(styles.headerSolid);
    }
    
    return baseStyle;
  };
  
  const getTitleStyle = () => {
    const baseStyle = [styles.title];
    
    switch (variant) {
      case 'large':
        baseStyle.push(styles.titleLarge);
        break;
      case 'compact':
        baseStyle.push(styles.titleCompact);
        break;
      default:
        baseStyle.push(styles.titleDefault);
    }
    
    return baseStyle;
  };
  
  const getGradientColors = () => {
    return theme.colors.gradients.dark;
  };
  
  const renderLeftComponent = () => {
    if (!leftIcon && !onLeftPress) return <View style={styles.sideComponent} />;
    
    return (
      <TouchableOpacity
        style={styles.sideComponent}
        onPress={onLeftPress}
        hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
      >
        <Ionicons
          name={leftIcon}
          size={24}
          color={transparent ? theme.colors.text.inverse : theme.colors.text.inverse}
        />
      </TouchableOpacity>
    );
  };
  
  const renderCenterComponent = () => {
    if (centerComponent) {
      return <View style={styles.centerComponent}>{centerComponent}</View>;
    }
    
    return (
      <View style={styles.centerComponent}>
        <Text style={[getTitleStyle(), titleStyle]} numberOfLines={1}>
          {title}
        </Text>
        {subtitle && (
          <Text style={styles.subtitle} numberOfLines={1}>
            {subtitle}
          </Text>
        )}
      </View>
    );
  };
  
  const renderRightComponent = () => {
    if (rightComponent) {
      return <View style={styles.sideComponent}>{rightComponent}</View>;
    }
    
    if (!rightIcon && !onRightPress) return <View style={styles.sideComponent} />;
    
    return (
      <TouchableOpacity
        style={styles.sideComponent}
        onPress={onRightPress}
        hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
      >
        <Ionicons
          name={rightIcon}
          size={24}
          color={transparent ? theme.colors.text.inverse : theme.colors.text.inverse}
        />
      </TouchableOpacity>
    );
  };
  
  const HeaderContent = () => (
    <View style={[getHeaderStyle(), style]} {...props}>
      <StatusBar
        barStyle={transparent ? 'light-content' : 'light-content'}
        backgroundColor="transparent"
        translucent
      />
      <View style={styles.headerContent}>
        {renderLeftComponent()}
        {renderCenterComponent()}
        {renderRightComponent()}
      </View>
    </View>
  );
  
  if (blur && Platform.OS === 'ios') {
    return (
      <BlurView intensity={80} style={[getHeaderStyle(), style]}>
        <StatusBar
          barStyle="light-content"
          backgroundColor="transparent"
          translucent
        />
        <View style={styles.headerContent}>
          {renderLeftComponent()}
          {renderCenterComponent()}
          {renderRightComponent()}
        </View>
      </BlurView>
    );
  }
  
  if (gradient) {
    return (
      <LinearGradient
        colors={getGradientColors()}
        style={[getHeaderStyle(), style]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        {...props}
      >
        <StatusBar
          barStyle="light-content"
          backgroundColor="transparent"
          translucent
        />
        <View style={styles.headerContent}>
          {renderLeftComponent()}
          {renderCenterComponent()}
          {renderRightComponent()}
        </View>
      </LinearGradient>
    );
  }
  
  return <HeaderContent />;
};

const styles = StyleSheet.create({
  header: {
    paddingHorizontal: theme.spacing.lg,
    paddingBottom: theme.spacing.md,
    ...theme.shadows.sm,
  },
  headerDefault: {
    minHeight: theme.layout.header.default,
  },
  headerLarge: {
    minHeight: theme.layout.header.large,
  },
  headerCompact: {
    minHeight: theme.layout.header.compact,
  },
  headerSolid: {
    backgroundColor: theme.colors.background.dark,
  },
  headerTransparent: {
    backgroundColor: 'transparent',
  },
  
  headerContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  
  sideComponent: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  centerComponent: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: theme.spacing.md,
  },
  
  title: {
    fontFamily: theme.typography.fonts.bold,
    color: theme.colors.text.inverse,
    textAlign: 'center',
    fontWeight: theme.typography.weights.bold,
  },
  titleDefault: {
    fontSize: theme.typography.sizes.lg,
  },
  titleLarge: {
    fontSize: theme.typography.sizes['2xl'],
  },
  titleCompact: {
    fontSize: theme.typography.sizes.md,
  },
  
  subtitle: {
    fontSize: theme.typography.sizes.sm,
    color: theme.colors.secondary.light,
    textAlign: 'center',
    marginTop: theme.spacing.xs,
    fontFamily: theme.typography.fonts.regular,
  },
});

export default Header;
