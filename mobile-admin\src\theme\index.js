// Enhanced Design System for Construction Admin App
import { Dimensions, Platform } from 'react-native';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

// Color Palette - Construction Industry Theme
export const colors = {
  // Primary Colors
  primary: {
    main: '#FFB703',      // Construction Orange
    light: '#FFD23F',     // Light Orange
    dark: '#E6A500',      // Dark Orange
    contrast: '#023047',   // Dark Blue for contrast
  },
  
  // Secondary Colors
  secondary: {
    main: '#8ECAE6',      // Light Blue
    light: '#B8E0F5',     // Very Light Blue
    dark: '#219EBC',      // Medium Blue
    contrast: '#023047',   // Dark Blue
  },
  
  // Accent Colors
  accent: {
    success: '#4CAF50',   // Green
    warning: '#FF9800',   // Orange
    error: '#F44336',     // Red
    info: '#2196F3',      // Blue
  },
  
  // Neutral Colors
  neutral: {
    white: '#FFFFFF',
    light: '#F8F9FA',     // Very Light Gray
    medium: '#E9ECEF',    // Light Gray
    dark: '#6C757D',      // Medium Gray
    darker: '#495057',    // Dark Gray
    black: '#212529',     // Almost Black
  },
  
  // Text Colors
  text: {
    primary: '#023047',   // Dark Blue
    secondary: '#495057', // Dark Gray
    disabled: '#6C757D',  // Medium Gray
    inverse: '#FFFFFF',   // White
    link: '#219EBC',      // Medium Blue
  },
  
  // Background Colors
  background: {
    primary: '#FFFFFF',   // White
    secondary: '#F8F9FA', // Very Light Gray
    tertiary: '#E9ECEF',  // Light Gray
    dark: '#023047',      // Dark Blue
    overlay: 'rgba(2, 48, 71, 0.8)', // Dark Blue with opacity
  },
  
  // Border Colors
  border: {
    light: '#E9ECEF',     // Light Gray
    medium: '#DEE2E6',    // Medium Gray
    dark: '#6C757D',      // Dark Gray
    primary: '#FFB703',   // Orange
  },
  
  // Shadow Colors
  shadow: {
    light: 'rgba(0, 0, 0, 0.1)',
    medium: 'rgba(0, 0, 0, 0.15)',
    dark: 'rgba(0, 0, 0, 0.25)',
  },
  
  // Gradient Colors
  gradients: {
    primary: ['#FFB703', '#FFD23F'],
    secondary: ['#8ECAE6', '#B8E0F5'],
    dark: ['#023047', '#219EBC'],
    sunset: ['#FFB703', '#FF9800', '#F44336'],
    ocean: ['#8ECAE6', '#219EBC', '#023047'],
  },
};

// Typography System
export const typography = {
  // Font Families
  fonts: {
    regular: Platform.OS === 'ios' ? 'System' : 'Roboto',
    medium: Platform.OS === 'ios' ? 'System' : 'Roboto-Medium',
    bold: Platform.OS === 'ios' ? 'System' : 'Roboto-Bold',
    light: Platform.OS === 'ios' ? 'System' : 'Roboto-Light',
    mono: Platform.OS === 'ios' ? 'Courier' : 'monospace',
  },
  
  // Font Sizes
  sizes: {
    xs: 10,
    sm: 12,
    md: 14,
    lg: 16,
    xl: 18,
    '2xl': 20,
    '3xl': 24,
    '4xl': 28,
    '5xl': 32,
    '6xl': 36,
    '7xl': 48,
  },
  
  // Line Heights
  lineHeights: {
    tight: 1.2,
    normal: 1.4,
    relaxed: 1.6,
    loose: 1.8,
  },
  
  // Font Weights
  weights: {
    light: '300',
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
    extrabold: '800',
  },
};

// Spacing System (based on 4px grid)
export const spacing = {
  xs: 4,
  sm: 8,
  md: 12,
  lg: 16,
  xl: 20,
  '2xl': 24,
  '3xl': 32,
  '4xl': 40,
  '5xl': 48,
  '6xl': 64,
  '7xl': 80,
  '8xl': 96,
};

// Border Radius System
export const borderRadius = {
  none: 0,
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  '2xl': 20,
  '3xl': 24,
  full: 9999,
};

// Shadow System
export const shadows = {
  none: {
    shadowColor: 'transparent',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },
  sm: {
    shadowColor: colors.shadow.light,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 1,
    shadowRadius: 2,
    elevation: 2,
  },
  md: {
    shadowColor: colors.shadow.medium,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 1,
    shadowRadius: 4,
    elevation: 4,
  },
  lg: {
    shadowColor: colors.shadow.medium,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 1,
    shadowRadius: 8,
    elevation: 8,
  },
  xl: {
    shadowColor: colors.shadow.dark,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 1,
    shadowRadius: 16,
    elevation: 16,
  },
};

// Layout System
export const layout = {
  // Screen Dimensions
  screen: {
    width: screenWidth,
    height: screenHeight,
  },
  
  // Breakpoints
  breakpoints: {
    sm: 576,
    md: 768,
    lg: 992,
    xl: 1200,
  },
  
  // Container Sizes
  container: {
    sm: screenWidth * 0.9,
    md: screenWidth * 0.85,
    lg: screenWidth * 0.8,
    xl: screenWidth * 0.75,
  },
  
  // Header Heights
  header: {
    default: 60,
    large: 80,
    compact: 50,
  },
  
  // Tab Bar Heights
  tabBar: {
    default: 60,
    compact: 50,
  },
};

// Animation Durations
export const animations = {
  fast: 150,
  normal: 250,
  slow: 350,
  slower: 500,
};

// Component Variants
export const variants = {
  // Button Variants
  button: {
    primary: {
      backgroundColor: colors.primary.main,
      borderColor: colors.primary.main,
    },
    secondary: {
      backgroundColor: colors.secondary.main,
      borderColor: colors.secondary.main,
    },
    outline: {
      backgroundColor: 'transparent',
      borderColor: colors.primary.main,
    },
    ghost: {
      backgroundColor: 'transparent',
      borderColor: 'transparent',
    },
  },
  
  // Card Variants
  card: {
    elevated: {
      backgroundColor: colors.background.primary,
      ...shadows.md,
    },
    flat: {
      backgroundColor: colors.background.primary,
      borderWidth: 1,
      borderColor: colors.border.light,
    },
    outlined: {
      backgroundColor: 'transparent',
      borderWidth: 2,
      borderColor: colors.border.medium,
    },
  },
};

// Export default theme object
export default {
  colors,
  typography,
  spacing,
  borderRadius,
  shadows,
  layout,
  animations,
  variants,
};
