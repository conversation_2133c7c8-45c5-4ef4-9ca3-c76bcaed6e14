// Environment configuration for the mobile app
import Constants from 'expo-constants';

// Get the environment from Expo constants or default to development
const ENV = {
  dev: {
    apiUrl: 'http://***********:8000/api', // Update with your local IP
    debug: true,
  },
  staging: {
    apiUrl: 'https://staging.constructco.com/api',
    debug: true,
  },
  prod: {
    apiUrl: 'https://api.constructco.com/api',
    debug: false,
  },
};

// Determine current environment
const getEnvVars = (env = Constants.releaseChannel) => {
  // If releasing to app store, use prod environment
  if (env === 'default' || env === undefined) {
    return ENV.dev; // Development environment
  }
  if (env.indexOf('staging') !== -1) {
    return ENV.staging;
  }
  if (env.indexOf('prod') !== -1) {
    return ENV.prod;
  }
  return ENV.dev; // Default to development
};

export default getEnvVars;
