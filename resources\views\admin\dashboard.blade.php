@extends('layouts.admin')

@section('title', 'Dashboard - Admin Panel')
@section('page-title', 'Dashboard')

@section('content')
<div class="row">
    <!-- Statistics Cards -->
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3>{{ $stats['total_projects'] }}</h3>
                    <p class="mb-0 fw-semibold">Total Projects</p>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-building fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="text-success">{{ $stats['active_projects'] }}</h3>
                        <p class="mb-0 text-muted">Active Projects</p>
                    </div>
                    <div class="text-success">
                        <i class="fas fa-play-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="text-info">{{ $stats['total_services'] }}</h3>
                        <p class="mb-0 text-muted">Services</p>
                    </div>
                    <div class="text-info">
                        <i class="fas fa-tools fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="text-warning">{{ $stats['new_messages'] }}</h3>
                        <p class="mb-0 text-muted">New Messages</p>
                    </div>
                    <div class="text-warning">
                        <i class="fas fa-envelope fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Recent Projects -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Recent Projects</h5>
                <a href="{{ route('admin.projects') }}" class="btn btn-sm btn-outline-primary">View All</a>
            </div>
            <div class="card-body">
                @if($recentProjects->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Project</th>
                                    <th>Category</th>
                                    <th>Status</th>
                                    <th>Budget</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($recentProjects as $project)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            @if($project->images && count($project->images) > 0)
                                                <img src="{{ asset('storage/' . $project->images[0]) }}" 
                                                     alt="{{ $project->title }}" 
                                                     class="rounded me-3" 
                                                     style="width: 50px; height: 50px; object-fit: cover;">
                                            @else
                                                <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center" 
                                                     style="width: 50px; height: 50px;">
                                                    <i class="fas fa-building text-muted"></i>
                                                </div>
                                            @endif
                                            <div>
                                                <h6 class="mb-0">{{ $project->title }}</h6>
                                                <small class="text-muted">{{ Str::limit($project->short_description, 50) }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        @if($project->category)
                                            <span class="badge bg-light text-dark">{{ $project->category->name }}</span>
                                        @else
                                            <span class="text-muted">-</span>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge 
                                            @if($project->status == 'completed') bg-success 
                                            @elseif($project->status == 'in_progress') bg-warning 
                                            @elseif($project->status == 'planning') bg-info 
                                            @else bg-secondary @endif">
                                            {{ ucfirst(str_replace('_', ' ', $project->status)) }}
                                        </span>
                                    </td>
                                    <td>
                                        @if($project->budget)
                                            ${{ number_format($project->budget) }}
                                        @else
                                            <span class="text-muted">-</span>
                                        @endif
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ $project->created_at->format('M d, Y') }}</small>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-building fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No projects yet</p>
                        <a href="{{ route('admin.projects') }}" class="btn btn-primary">Add First Project</a>
                    </div>
                @endif
            </div>
        </div>
    </div>
    
    <!-- Recent Messages -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Recent Messages</h5>
                <a href="{{ route('admin.messages') }}" class="btn btn-sm btn-outline-primary">View All</a>
            </div>
            <div class="card-body">
                @if($recentMessages->count() > 0)
                    @foreach($recentMessages as $message)
                    <div class="d-flex align-items-start mb-3 pb-3 border-bottom">
                        <div class="flex-shrink-0">
                            <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center" 
                                 style="width: 40px; height: 40px;">
                                <i class="fas fa-user text-white"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="d-flex justify-content-between align-items-start">
                                <h6 class="mb-1">{{ $message->name }}</h6>
                                <span class="badge 
                                    @if($message->status == 'new') bg-warning 
                                    @elseif($message->status == 'read') bg-info 
                                    @elseif($message->status == 'replied') bg-success 
                                    @else bg-secondary @endif">
                                    {{ ucfirst($message->status) }}
                                </span>
                            </div>
                            <p class="mb-1 small">{{ $message->subject }}</p>
                            <small class="text-muted">{{ $message->created_at->diffForHumans() }}</small>
                        </div>
                    </div>
                    @endforeach
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-envelope fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No messages yet</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-lg-3 col-md-6">
                        <a href="{{ route('admin.projects') }}" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="fas fa-plus-circle fa-2x mb-2"></i>
                            <span>Add New Project</span>
                        </a>
                    </div>
                    
                    <div class="col-lg-3 col-md-6">
                        <a href="{{ route('admin.services') }}" class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="fas fa-tools fa-2x mb-2"></i>
                            <span>Manage Services</span>
                        </a>
                    </div>
                    
                    <div class="col-lg-3 col-md-6">
                        <a href="{{ route('admin.media') }}" class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="fas fa-upload fa-2x mb-2"></i>
                            <span>Upload Media</span>
                        </a>
                    </div>
                    
                    <div class="col-lg-3 col-md-6">
                        <a href="{{ route('admin.messages') }}" class="btn btn-outline-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="fas fa-envelope-open fa-2x mb-2"></i>
                            <span>Check Messages</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Additional Stats -->
<div class="row mt-4">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Project Status Overview</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-3">
                        <h4 class="text-info">{{ $stats['total_projects'] }}</h4>
                        <small class="text-muted">Total</small>
                    </div>
                    <div class="col-3">
                        <h4 class="text-warning">{{ $stats['active_projects'] }}</h4>
                        <small class="text-muted">Active</small>
                    </div>
                    <div class="col-3">
                        <h4 class="text-success">{{ $stats['completed_projects'] }}</h4>
                        <small class="text-muted">Completed</small>
                    </div>
                    <div class="col-3">
                        <h4 class="text-secondary">{{ $stats['total_projects'] - $stats['active_projects'] - $stats['completed_projects'] }}</h4>
                        <small class="text-muted">Other</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Content Overview</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-4">
                        <h4 class="text-primary">{{ $stats['total_services'] }}</h4>
                        <small class="text-muted">Services</small>
                    </div>
                    <div class="col-4">
                        <h4 class="text-info">{{ $stats['total_media'] }}</h4>
                        <small class="text-muted">Media Files</small>
                    </div>
                    <div class="col-4">
                        <h4 class="text-warning">{{ $stats['total_messages'] }}</h4>
                        <small class="text-muted">Messages</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
