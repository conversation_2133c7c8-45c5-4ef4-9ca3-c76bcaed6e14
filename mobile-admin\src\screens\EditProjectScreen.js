import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Picker } from '@react-native-picker/picker';
import * as ImagePicker from 'expo-image-picker';
import { apiService } from '../services/apiService';

const EditProjectScreen = ({ route, navigation }) => {
  const { projectId } = route.params;
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [categories, setCategories] = useState([]);
  const [project, setProject] = useState({
    title: '',
    description: '',
    location: '',
    budget: '',
    status: 'planning',
    category_id: '',
    start_date: '',
    end_date: '',
    progress: 0,
    images: [],
  });
  const [newImages, setNewImages] = useState([]);

  useEffect(() => {
    loadProject();
    loadCategories();
  }, []);

  const loadProject = async () => {
    try {
      const response = await apiService.getProjectForEdit(projectId);
      setProject(response);
    } catch (error) {
      console.error('Error loading project:', error);
      Alert.alert('Error', 'Failed to load project details');
      navigation.goBack();
    } finally {
      setLoading(false);
    }
  };

  const loadCategories = async () => {
    try {
      const response = await apiService.getCategories();
      setCategories(response.data || []);
    } catch (error) {
      console.error('Error loading categories:', error);
    }
  };

  const pickImages = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        allowsEditing: false,
        quality: 0.8,
        allowsMultipleSelection: true,
      });

      if (!result.canceled && result.assets) {
        setNewImages([...newImages, ...result.assets]);
      }
    } catch (error) {
      console.error('Error picking images:', error);
      Alert.alert('Error', 'Failed to pick images');
    }
  };

  const removeNewImage = (index) => {
    const updatedImages = newImages.filter((_, i) => i !== index);
    setNewImages(updatedImages);
  };

  const removeExistingImage = (index) => {
    const updatedImages = project.images.filter((_, i) => i !== index);
    setProject({ ...project, images: updatedImages });
  };

  const handleSave = async () => {
    if (!project.title.trim()) {
      Alert.alert('Error', 'Please enter a project title');
      return;
    }

    if (!project.category_id) {
      Alert.alert('Error', 'Please select a category');
      return;
    }

    setSaving(true);
    try {
      const formData = new FormData();

      // Add project data
      formData.append('title', project.title);
      formData.append('description', project.description);
      formData.append('location', project.location);
      formData.append('budget', project.budget);
      formData.append('status', project.status);
      formData.append('category_id', project.category_id);
      formData.append('start_date', project.start_date);
      formData.append('end_date', project.end_date);
      formData.append('progress', project.progress.toString());

      // Add new images
      newImages.forEach((image, index) => {
        formData.append('images[]', {
          uri: image.uri,
          type: 'image/jpeg',
          name: `image_${index}.jpg`,
        });
      });

      await apiService.updateProject(projectId, formData);

      Alert.alert('Success', 'Project updated successfully', [
        { text: 'OK', onPress: () => navigation.goBack() }
      ]);
    } catch (error) {
      console.error('Error updating project:', error);
      Alert.alert('Error', 'Failed to update project');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#FFB703" />
        <Text style={styles.loadingText}>Loading project...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color="#023047" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Edit Project</Text>
        <TouchableOpacity onPress={handleSave} disabled={saving}>
          {saving ? (
            <ActivityIndicator size="small" color="#FFB703" />
          ) : (
            <Ionicons name="checkmark" size={24} color="#FFB703" />
          )}
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.form}>
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Project Title *</Text>
            <TextInput
              style={styles.input}
              value={project.title}
              onChangeText={(text) => setProject({ ...project, title: text })}
              placeholder="Enter project title"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Category *</Text>
            <View style={styles.pickerContainer}>
              <Picker
                selectedValue={project.category_id}
                onValueChange={(value) => setProject({ ...project, category_id: value })}
                style={styles.picker}
              >
                <Picker.Item label="Select Category" value="" />
                {categories.map((category) => (
                  <Picker.Item
                    key={category.id}
                    label={category.name}
                    value={category.id}
                  />
                ))}
              </Picker>
            </View>
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Description</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={project.description}
              onChangeText={(text) => setProject({ ...project, description: text })}
              placeholder="Enter project description"
              multiline
              numberOfLines={4}
            />
          </View>

          <View style={styles.row}>
            <View style={[styles.inputGroup, styles.halfWidth]}>
              <Text style={styles.label}>Location</Text>
              <TextInput
                style={styles.input}
                value={project.location}
                onChangeText={(text) => setProject({ ...project, location: text })}
                placeholder="Project location"
              />
            </View>

            <View style={[styles.inputGroup, styles.halfWidth]}>
              <Text style={styles.label}>Budget</Text>
              <TextInput
                style={styles.input}
                value={project.budget}
                onChangeText={(text) => setProject({ ...project, budget: text })}
                placeholder="Budget amount"
                keyboardType="numeric"
              />
            </View>
          </View>

          <View style={styles.row}>
            <View style={[styles.inputGroup, styles.halfWidth]}>
              <Text style={styles.label}>Status</Text>
              <View style={styles.pickerContainer}>
                <Picker
                  selectedValue={project.status}
                  onValueChange={(value) => setProject({ ...project, status: value })}
                  style={styles.picker}
                >
                  <Picker.Item label="Planning" value="planning" />
                  <Picker.Item label="In Progress" value="in_progress" />
                  <Picker.Item label="Completed" value="completed" />
                  <Picker.Item label="On Hold" value="on_hold" />
                </Picker>
              </View>
            </View>

            <View style={[styles.inputGroup, styles.halfWidth]}>
              <Text style={styles.label}>Progress (%)</Text>
              <TextInput
                style={styles.input}
                value={project.progress.toString()}
                onChangeText={(text) => setProject({ ...project, progress: parseInt(text) || 0 })}
                placeholder="0-100"
                keyboardType="numeric"
              />
            </View>
          </View>

          <View style={styles.row}>
            <View style={[styles.inputGroup, styles.halfWidth]}>
              <Text style={styles.label}>Start Date</Text>
              <TextInput
                style={styles.input}
                value={project.start_date}
                onChangeText={(text) => setProject({ ...project, start_date: text })}
                placeholder="YYYY-MM-DD"
              />
            </View>

            <View style={[styles.inputGroup, styles.halfWidth]}>
              <Text style={styles.label}>End Date</Text>
              <TextInput
                style={styles.input}
                value={project.end_date}
                onChangeText={(text) => setProject({ ...project, end_date: text })}
                placeholder="YYYY-MM-DD"
              />
            </View>
          </View>

          {/* Existing Images */}
          {project.images && project.images.length > 0 && (
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Current Images</Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                <View style={styles.imagesContainer}>
                  {project.images.map((image, index) => (
                    <View key={index} style={styles.imageItem}>
                      <Image
                        source={{ uri: `http://192.168.0.3:8000/storage/${image}` }}
                        style={styles.image}
                      />
                      <TouchableOpacity
                        style={styles.removeImageButton}
                        onPress={() => removeExistingImage(index)}
                      >
                        <Ionicons name="close" size={16} color="#fff" />
                      </TouchableOpacity>
                    </View>
                  ))}
                </View>
              </ScrollView>
            </View>
          )}

          {/* New Images */}
          {newImages.length > 0 && (
            <View style={styles.inputGroup}>
              <Text style={styles.label}>New Images</Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                <View style={styles.imagesContainer}>
                  {newImages.map((image, index) => (
                    <View key={index} style={styles.imageItem}>
                      <Image source={{ uri: image.uri }} style={styles.image} />
                      <TouchableOpacity
                        style={styles.removeImageButton}
                        onPress={() => removeNewImage(index)}
                      >
                        <Ionicons name="close" size={16} color="#fff" />
                      </TouchableOpacity>
                    </View>
                  ))}
                </View>
              </ScrollView>
            </View>
          )}

          <TouchableOpacity style={styles.imagePickerButton} onPress={pickImages}>
            <Ionicons name="camera" size={20} color="#FFB703" />
            <Text style={styles.imagePickerText}>Add Images</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#023047',
  },
  content: {
    flex: 1,
  },
  form: {
    padding: 20,
  },
  inputGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#023047',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#fff',
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    backgroundColor: '#fff',
  },
  picker: {
    height: 50,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  halfWidth: {
    width: '48%',
  },
  imagesContainer: {
    flexDirection: 'row',
    paddingVertical: 10,
  },
  imageItem: {
    position: 'relative',
    marginRight: 10,
  },
  image: {
    width: 80,
    height: 80,
    borderRadius: 8,
  },
  removeImageButton: {
    position: 'absolute',
    top: -5,
    right: -5,
    backgroundColor: '#FF6B6B',
    borderRadius: 10,
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  imagePickerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: '#FFB703',
    borderStyle: 'dashed',
    borderRadius: 8,
    padding: 20,
    marginTop: 10,
  },
  imagePickerText: {
    marginLeft: 8,
    fontSize: 16,
    color: '#FFB703',
    fontWeight: '600',
  },
});

export default EditProjectScreen;
