@extends('layouts.admin')

@section('title', 'Media - Admin Panel')
@section('page-title', 'Media Management')

@section('content')
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="d-flex align-items-center">
            <h4 class="mb-0 me-3">Media Library</h4>
            <span class="badge bg-primary">{{ $media->total() }} Files</span>
        </div>
    </div>
    <div class="col-lg-4 text-end">
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadMediaModal">
            <i class="fas fa-upload me-2"></i>Upload Media
        </button>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="{{ route('admin.media') }}" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label">Search Files</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="{{ request('search') }}" placeholder="Search by filename...">
            </div>
            <div class="col-md-3">
                <label for="type" class="form-label">File Type</label>
                <select class="form-select" id="type" name="type">
                    <option value="">All Types</option>
                    <option value="image" {{ request('type') == 'image' ? 'selected' : '' }}>Images</option>
                    <option value="video" {{ request('type') == 'video' ? 'selected' : '' }}>Videos</option>
                    <option value="document" {{ request('type') == 'document' ? 'selected' : '' }}>Documents</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="sort" class="form-label">Sort By</label>
                <select class="form-select" id="sort" name="sort">
                    <option value="newest" {{ request('sort') == 'newest' ? 'selected' : '' }}>Newest First</option>
                    <option value="oldest" {{ request('sort') == 'oldest' ? 'selected' : '' }}>Oldest First</option>
                    <option value="name" {{ request('sort') == 'name' ? 'selected' : '' }}>Name A-Z</option>
                    <option value="size" {{ request('sort') == 'size' ? 'selected' : '' }}>File Size</option>
                </select>
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button type="submit" class="btn btn-outline-primary me-2">
                    <i class="fas fa-search"></i>
                </button>
                <a href="{{ route('admin.media') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-times"></i>
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Media Grid -->
<div class="row">
    @if($media->count() > 0)
        @foreach($media as $file)
        <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
            <div class="card h-100">
                <div class="position-relative">
                    @if(str_starts_with($file->file_type, 'image'))
                        <img src="{{ asset('storage/' . $file->file_path) }}" 
                             class="card-img-top" 
                             style="height: 200px; object-fit: cover;" 
                             alt="{{ $file->original_name }}">
                    @elseif(str_starts_with($file->file_type, 'video'))
                        <div class="card-img-top bg-dark d-flex align-items-center justify-content-center" 
                             style="height: 200px;">
                            <i class="fas fa-play-circle fa-3x text-white"></i>
                        </div>
                    @else
                        <div class="card-img-top bg-light d-flex align-items-center justify-content-center" 
                             style="height: 200px;">
                            <i class="fas fa-file fa-3x text-muted"></i>
                        </div>
                    @endif
                    
                    <!-- File type badge -->
                    <span class="position-absolute top-0 start-0 m-2 badge bg-dark">
                        {{ strtoupper(pathinfo($file->original_name, PATHINFO_EXTENSION)) }}
                    </span>
                    
                    <!-- Actions dropdown -->
                    <div class="position-absolute top-0 end-0 m-2">
                        <div class="dropdown">
                            <button class="btn btn-sm btn-dark" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{{ asset('storage/' . $file->file_path) }}" target="_blank">
                                    <i class="fas fa-eye me-2"></i>View
                                </a></li>
                                <li><a class="dropdown-item" href="{{ asset('storage/' . $file->file_path) }}" download>
                                    <i class="fas fa-download me-2"></i>Download
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="copyToClipboard('{{ asset('storage/' . $file->file_path) }}')">
                                    <i class="fas fa-copy me-2"></i>Copy URL
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger" href="#" onclick="deleteMedia({{ $file->id }})">
                                    <i class="fas fa-trash me-2"></i>Delete
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="card-body">
                    <h6 class="card-title mb-2" title="{{ $file->original_name }}">
                        {{ Str::limit($file->original_name, 25) }}
                    </h6>
                    
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">
                            {{ $file->file_size ? number_format($file->file_size / 1024, 1) . ' KB' : 'Unknown size' }}
                        </small>
                        <small class="text-muted">
                            {{ $file->created_at->format('M d, Y') }}
                        </small>
                    </div>
                    
                    @if($file->alt_text)
                        <small class="text-muted d-block mt-1">
                            Alt: {{ Str::limit($file->alt_text, 30) }}
                        </small>
                    @endif
                </div>
            </div>
        </div>
        @endforeach
        
        <!-- Pagination -->
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <small class="text-muted">
                        Showing {{ $media->firstItem() }} to {{ $media->lastItem() }} 
                        of {{ $media->total() }} files
                    </small>
                </div>
                <div>
                    {{ $media->links() }}
                </div>
            </div>
        </div>
    @else
        <div class="col-12">
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="fas fa-images fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No Media Files Found</h5>
                    <p class="text-muted">Start by uploading your first media file.</p>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadMediaModal">
                        <i class="fas fa-upload me-2"></i>Upload Media
                    </button>
                </div>
            </div>
        </div>
    @endif
</div>

<!-- Upload Media Modal -->
<div class="modal fade" id="uploadMediaModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Upload Media Files</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="uploadMediaForm" enctype="multipart/form-data">
                @csrf
                <div class="modal-body">
                    <div class="row g-3">
                        <div class="col-12">
                            <label for="files" class="form-label">Select Files *</label>
                            <input type="file" class="form-control" id="files" name="files[]" multiple 
                                   accept="image/*,video/*,.pdf,.doc,.docx" required>
                            <small class="text-muted">
                                Supported formats: Images (JPG, PNG, GIF), Videos (MP4, MOV), Documents (PDF, DOC, DOCX)
                            </small>
                        </div>
                        <div class="col-12">
                            <label for="alt_text" class="form-label">Alt Text (for images)</label>
                            <input type="text" class="form-control" id="alt_text" name="alt_text" 
                                   placeholder="Describe the image for accessibility">
                        </div>
                        <div class="col-12">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="3" 
                                      placeholder="Optional description for the files"></textarea>
                        </div>
                        <div class="col-12" id="file_preview">
                            <!-- File preview will be shown here -->
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-upload me-2"></i>Upload Files
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// File preview functionality
document.getElementById('files').addEventListener('change', function(e) {
    const files = e.target.files;
    const previewDiv = document.getElementById('file_preview');

    if (files.length > 0) {
        previewDiv.innerHTML = '<label class="form-label">File Preview</label><div class="d-flex flex-wrap gap-2" id="preview_container"></div>';
        const container = document.getElementById('preview_container');

        Array.from(files).forEach((file, index) => {
            const fileDiv = document.createElement('div');
            fileDiv.className = 'text-center';

            if (file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    fileDiv.innerHTML = `
                        <img src="${e.target.result}" class="rounded mb-1" style="width: 80px; height: 80px; object-fit: cover;">
                        <div><small class="text-muted">${file.name}</small></div>
                    `;
                };
                reader.readAsDataURL(file);
            } else {
                fileDiv.innerHTML = `
                    <div class="bg-light rounded mb-1 d-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                        <i class="fas fa-file fa-2x text-muted"></i>
                    </div>
                    <div><small class="text-muted">${file.name}</small></div>
                `;
            }

            container.appendChild(fileDiv);
        });
    } else {
        previewDiv.innerHTML = '';
    }
});

// Upload Media Form
document.getElementById('uploadMediaForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;

    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Uploading...';
    submitBtn.disabled = true;

    try {
        const response = await fetch('{{ route("admin.media.store") }}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });

        const result = await response.json();

        if (response.ok) {
            bootstrap.Modal.getInstance(document.getElementById('uploadMediaModal')).hide();
            location.reload();
        } else {
            alert(result.message || 'Error uploading files');
        }
    } catch (error) {
        alert('Error uploading files');
    } finally {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }
});

// Copy to clipboard function
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show success message
        const toast = document.createElement('div');
        toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed top-0 end-0 m-3';
        toast.style.zIndex = '9999';
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    URL copied to clipboard!
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;
        document.body.appendChild(toast);

        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();

        // Remove toast after it's hidden
        toast.addEventListener('hidden.bs.toast', function() {
            document.body.removeChild(toast);
        });
    }).catch(function(err) {
        alert('Failed to copy URL to clipboard');
    });
}

// Delete Media
async function deleteMedia(id) {
    if (!confirm('Are you sure you want to delete this media file? This action cannot be undone.')) {
        return;
    }

    try {
        const response = await fetch(`/admin/media/${id}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();

        if (response.ok) {
            location.reload();
        } else {
            alert(result.message || 'Error deleting media file');
        }
    } catch (error) {
        alert('Error deleting media file');
    }
}

// Reset form when modal is closed
document.getElementById('uploadMediaModal').addEventListener('hidden.bs.modal', function() {
    document.getElementById('uploadMediaForm').reset();
    document.getElementById('file_preview').innerHTML = '';
});
</script>
@endpush
