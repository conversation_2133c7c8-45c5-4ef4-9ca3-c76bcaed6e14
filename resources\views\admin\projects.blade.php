@extends('layouts.admin')

@section('title', 'Projects - Admin Panel')
@section('page-title', 'Projects Management')

@section('content')
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="d-flex align-items-center">
            <h4 class="mb-0 me-3">Projects</h4>
            <span class="badge bg-primary">{{ $projects->total() }} Total</span>
        </div>
    </div>
    <div class="col-lg-4 text-end">
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addProjectModal">
            <i class="fas fa-plus me-2"></i>Add New Project
        </button>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="{{ route('admin.projects') }}" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label">Search Projects</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="{{ request('search') }}" placeholder="Search by title...">
            </div>
            <div class="col-md-3">
                <label for="status" class="form-label">Status</label>
                <select class="form-select" id="status" name="status">
                    <option value="">All Status</option>
                    <option value="planning" {{ request('status') == 'planning' ? 'selected' : '' }}>Planning</option>
                    <option value="in_progress" {{ request('status') == 'in_progress' ? 'selected' : '' }}>In Progress</option>
                    <option value="completed" {{ request('status') == 'completed' ? 'selected' : '' }}>Completed</option>
                    <option value="on_hold" {{ request('status') == 'on_hold' ? 'selected' : '' }}>On Hold</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="category" class="form-label">Category</label>
                <select class="form-select" id="category" name="category">
                    <option value="">All Categories</option>
                    @foreach(\App\Models\Category::all() as $category)
                        <option value="{{ $category->id }}" {{ request('category') == $category->id ? 'selected' : '' }}>
                            {{ $category->name }}
                        </option>
                    @endforeach
                </select>
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button type="submit" class="btn btn-outline-primary me-2">
                    <i class="fas fa-search"></i>
                </button>
                <a href="{{ route('admin.projects') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-times"></i>
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Projects Table -->
<div class="card">
    <div class="card-body">
        @if($projects->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Project</th>
                            <th>Category</th>
                            <th>Status</th>
                            <th>Budget</th>
                            <th>Progress</th>
                            <th>Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($projects as $project)
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    @if($project->images && count($project->images) > 0)
                                        <img src="{{ asset('storage/' . $project->images[0]) }}" 
                                             alt="{{ $project->title }}" 
                                             class="rounded me-3" 
                                             style="width: 50px; height: 50px; object-fit: cover;">
                                    @else
                                        <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center" 
                                             style="width: 50px; height: 50px;">
                                            <i class="fas fa-building text-muted"></i>
                                        </div>
                                    @endif
                                    <div>
                                        <h6 class="mb-1">{{ $project->title }}</h6>
                                        <small class="text-muted">{{ $project->location }}</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-secondary">{{ $project->category->name ?? 'Uncategorized' }}</span>
                            </td>
                            <td>
                                @php
                                    $statusColors = [
                                        'planning' => 'warning',
                                        'in_progress' => 'primary',
                                        'completed' => 'success',
                                        'on_hold' => 'danger'
                                    ];
                                @endphp
                                <span class="badge bg-{{ $statusColors[$project->status] ?? 'secondary' }}">
                                    {{ ucfirst(str_replace('_', ' ', $project->status)) }}
                                </span>
                            </td>
                            <td>
                                @if($project->budget)
                                    ${{ number_format($project->budget) }}
                                @else
                                    <span class="text-muted">Not set</span>
                                @endif
                            </td>
                            <td>
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar" role="progressbar" 
                                         style="width: {{ $project->progress ?? 0 }}%"
                                         aria-valuenow="{{ $project->progress ?? 0 }}" 
                                         aria-valuemin="0" aria-valuemax="100">
                                    </div>
                                </div>
                                <small class="text-muted">{{ $project->progress ?? 0 }}%</small>
                            </td>
                            <td>
                                <small class="text-muted">{{ $project->created_at->format('M d, Y') }}</small>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary" 
                                            onclick="editProject({{ $project->id }})"
                                            data-bs-toggle="modal" 
                                            data-bs-target="#editProjectModal">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-danger" 
                                            onclick="deleteProject({{ $project->id }})">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center mt-4">
                <div>
                    <small class="text-muted">
                        Showing {{ $projects->firstItem() }} to {{ $projects->lastItem() }} 
                        of {{ $projects->total() }} results
                    </small>
                </div>
                <div>
                    {{ $projects->links() }}
                </div>
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-building fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No Projects Found</h5>
                <p class="text-muted">Start by adding your first project.</p>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addProjectModal">
                    <i class="fas fa-plus me-2"></i>Add New Project
                </button>
            </div>
        @endif
    </div>
</div>

<!-- Add Project Modal -->
<div class="modal fade" id="addProjectModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Project</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addProjectForm" enctype="multipart/form-data">
                @csrf
                <div class="modal-body">
                    <div class="row g-3">
                        <div class="col-md-8">
                            <label for="title" class="form-label">Project Title *</label>
                            <input type="text" class="form-control" id="title" name="title" required>
                        </div>
                        <div class="col-md-4">
                            <label for="category_id" class="form-label">Category *</label>
                            <select class="form-select" id="category_id" name="category_id" required>
                                <option value="">Select Category</option>
                                @foreach(\App\Models\Category::all() as $category)
                                    <option value="{{ $category->id }}">{{ $category->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="location" class="form-label">Location</label>
                            <input type="text" class="form-control" id="location" name="location">
                        </div>
                        <div class="col-md-3">
                            <label for="budget" class="form-label">Budget</label>
                            <input type="number" class="form-control" id="budget" name="budget" min="0">
                        </div>
                        <div class="col-md-3">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="status" name="status">
                                <option value="planning">Planning</option>
                                <option value="in_progress">In Progress</option>
                                <option value="completed">Completed</option>
                                <option value="on_hold">On Hold</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="4"></textarea>
                        </div>
                        <div class="col-md-6">
                            <label for="start_date" class="form-label">Start Date</label>
                            <input type="date" class="form-control" id="start_date" name="start_date">
                        </div>
                        <div class="col-md-6">
                            <label for="end_date" class="form-label">End Date</label>
                            <input type="date" class="form-control" id="end_date" name="end_date">
                        </div>
                        <div class="col-12">
                            <label for="images" class="form-label">Project Images</label>
                            <input type="file" class="form-control" id="images" name="images[]" multiple accept="image/*">
                            <small class="text-muted">You can select multiple images</small>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Save Project
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Project Modal -->
<div class="modal fade" id="editProjectModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Project</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editProjectForm" enctype="multipart/form-data">
                @csrf
                @method('PUT')
                <input type="hidden" id="edit_project_id" name="project_id">
                <div class="modal-body">
                    <div class="row g-3">
                        <div class="col-md-8">
                            <label for="edit_title" class="form-label">Project Title *</label>
                            <input type="text" class="form-control" id="edit_title" name="title" required>
                        </div>
                        <div class="col-md-4">
                            <label for="edit_category_id" class="form-label">Category *</label>
                            <select class="form-select" id="edit_category_id" name="category_id" required>
                                <option value="">Select Category</option>
                                @foreach(\App\Models\Category::all() as $category)
                                    <option value="{{ $category->id }}">{{ $category->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="edit_location" class="form-label">Location</label>
                            <input type="text" class="form-control" id="edit_location" name="location">
                        </div>
                        <div class="col-md-3">
                            <label for="edit_budget" class="form-label">Budget</label>
                            <input type="number" class="form-control" id="edit_budget" name="budget" min="0">
                        </div>
                        <div class="col-md-3">
                            <label for="edit_status" class="form-label">Status</label>
                            <select class="form-select" id="edit_status" name="status">
                                <option value="planning">Planning</option>
                                <option value="in_progress">In Progress</option>
                                <option value="completed">Completed</option>
                                <option value="on_hold">On Hold</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <label for="edit_description" class="form-label">Description</label>
                            <textarea class="form-control" id="edit_description" name="description" rows="4"></textarea>
                        </div>
                        <div class="col-md-6">
                            <label for="edit_start_date" class="form-label">Start Date</label>
                            <input type="date" class="form-control" id="edit_start_date" name="start_date">
                        </div>
                        <div class="col-md-6">
                            <label for="edit_end_date" class="form-label">End Date</label>
                            <input type="date" class="form-control" id="edit_end_date" name="end_date">
                        </div>
                        <div class="col-md-6">
                            <label for="edit_progress" class="form-label">Progress (%)</label>
                            <input type="number" class="form-control" id="edit_progress" name="progress" min="0" max="100">
                        </div>
                        <div class="col-md-6">
                            <label for="edit_images" class="form-label">Add More Images</label>
                            <input type="file" class="form-control" id="edit_images" name="images[]" multiple accept="image/*">
                        </div>
                        <div class="col-12" id="current_images">
                            <!-- Current images will be loaded here -->
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Update Project
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Add Project Form
document.getElementById('addProjectForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;

    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Saving...';
    submitBtn.disabled = true;

    try {
        const response = await fetch('{{ route("admin.projects.store") }}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });

        const result = await response.json();

        if (response.ok) {
            bootstrap.Modal.getInstance(document.getElementById('addProjectModal')).hide();
            location.reload();
        } else {
            alert(result.message || 'Error saving project');
        }
    } catch (error) {
        alert('Error saving project');
    } finally {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }
});

// Edit Project
async function editProject(id) {
    try {
        const response = await fetch(`/admin/projects/${id}/edit`);
        const project = await response.json();

        document.getElementById('edit_project_id').value = project.id;
        document.getElementById('edit_title').value = project.title;
        document.getElementById('edit_category_id').value = project.category_id;
        document.getElementById('edit_location').value = project.location || '';
        document.getElementById('edit_budget').value = project.budget || '';
        document.getElementById('edit_status').value = project.status;
        document.getElementById('edit_description').value = project.description || '';
        document.getElementById('edit_start_date').value = project.start_date || '';
        document.getElementById('edit_end_date').value = project.end_date || '';
        document.getElementById('edit_progress').value = project.progress || 0;

        // Load current images
        const currentImagesDiv = document.getElementById('current_images');
        if (project.images && project.images.length > 0) {
            currentImagesDiv.innerHTML = '<label class="form-label">Current Images</label><div class="d-flex flex-wrap gap-2"></div>';
            const imagesContainer = currentImagesDiv.querySelector('div');

            project.images.forEach((image, index) => {
                const imageDiv = document.createElement('div');
                imageDiv.className = 'position-relative';
                imageDiv.innerHTML = `
                    <img src="/storage/${image}" class="rounded" style="width: 80px; height: 80px; object-fit: cover;">
                    <button type="button" class="btn btn-sm btn-danger position-absolute top-0 end-0"
                            onclick="removeImage(${project.id}, '${image}')" style="transform: translate(50%, -50%);">
                        <i class="fas fa-times"></i>
                    </button>
                `;
                imagesContainer.appendChild(imageDiv);
            });
        } else {
            currentImagesDiv.innerHTML = '';
        }
    } catch (error) {
        alert('Error loading project data');
    }
}

// Update Project Form
document.getElementById('editProjectForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const projectId = document.getElementById('edit_project_id').value;
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;

    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Updating...';
    submitBtn.disabled = true;

    try {
        const response = await fetch(`/admin/projects/${projectId}`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });

        const result = await response.json();

        if (response.ok) {
            bootstrap.Modal.getInstance(document.getElementById('editProjectModal')).hide();
            location.reload();
        } else {
            alert(result.message || 'Error updating project');
        }
    } catch (error) {
        alert('Error updating project');
    } finally {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }
});

// Delete Project
async function deleteProject(id) {
    if (!confirm('Are you sure you want to delete this project? This action cannot be undone.')) {
        return;
    }

    try {
        const response = await fetch(`/admin/projects/${id}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();

        if (response.ok) {
            location.reload();
        } else {
            alert(result.message || 'Error deleting project');
        }
    } catch (error) {
        alert('Error deleting project');
    }
}

// Remove Image
async function removeImage(projectId, imagePath) {
    if (!confirm('Are you sure you want to remove this image?')) {
        return;
    }

    try {
        const response = await fetch(`/admin/projects/${projectId}/remove-image`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ image: imagePath })
        });

        const result = await response.json();

        if (response.ok) {
            editProject(projectId); // Reload the edit form
        } else {
            alert(result.message || 'Error removing image');
        }
    } catch (error) {
        alert('Error removing image');
    }
}
</script>
@endpush
