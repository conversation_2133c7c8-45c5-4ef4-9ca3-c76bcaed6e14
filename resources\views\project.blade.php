@extends('layouts.app')

@section('title', $project->title . ' - Project Details')
@section('description', $project->short_description ?: Str::limit($project->description, 160))

@section('content')
<!-- Project Hero Section -->
<section class="project-hero-section">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <nav aria-label="breadcrumb" class="mb-4">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ route('home') }}">Home</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('projects') }}">Projects</a></li>
                        <li class="breadcrumb-item active" aria-current="page">{{ $project->title }}</li>
                    </ol>
                </nav>
                
                <div class="row align-items-center">
                    <div class="col-lg-8">
                        <div class="d-flex align-items-center mb-3">
                            <h1 class="display-5 fw-bold mb-0 me-3">{{ $project->title }}</h1>
                            @if($project->status)
                                <span class="badge fs-6 
                                    @if($project->status == 'completed') bg-success 
                                    @elseif($project->status == 'in_progress') bg-warning 
                                    @elseif($project->status == 'planning') bg-info 
                                    @else bg-secondary @endif">
                                    {{ ucfirst(str_replace('_', ' ', $project->status)) }}
                                </span>
                            @endif
                        </div>
                        
                        @if($project->short_description)
                            <p class="lead text-muted">{{ $project->short_description }}</p>
                        @endif
                        
                        <div class="project-quick-info">
                            <div class="row g-3">
                                @if($project->category)
                                    <div class="col-auto">
                                        <span class="badge bg-primary fs-6">
                                            <i class="fas fa-tag me-1"></i>{{ $project->category->name }}
                                        </span>
                                    </div>
                                @endif
                                
                                @if($project->location)
                                    <div class="col-auto">
                                        <span class="text-muted">
                                            <i class="fas fa-map-marker-alt me-1"></i>{{ $project->location }}
                                        </span>
                                    </div>
                                @endif
                                
                                @if($project->start_date)
                                    <div class="col-auto">
                                        <span class="text-muted">
                                            <i class="fas fa-calendar me-1"></i>
                                            {{ $project->start_date->format('M Y') }}
                                            @if($project->end_date)
                                                - {{ $project->end_date->format('M Y') }}
                                            @endif
                                        </span>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-4 text-lg-end">
                        @if($project->budget)
                            <div class="project-budget">
                                <h5 class="text-muted mb-1">Project Budget</h5>
                                <h3 class="fw-bold text-primary">${{ number_format($project->budget) }}</h3>
                            </div>
                        @endif
                        
                        @if($project->progress && $project->status == 'in_progress')
                            <div class="project-progress mt-3">
                                <h6 class="text-muted mb-2">Progress</h6>
                                <div class="progress" style="height: 10px;">
                                    <div class="progress-bar bg-warning" role="progressbar" 
                                         style="width: {{ $project->progress }}%" 
                                         aria-valuenow="{{ $project->progress }}" 
                                         aria-valuemin="0" aria-valuemax="100">
                                    </div>
                                </div>
                                <small class="text-muted">{{ $project->progress }}% Complete</small>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Project Images Gallery -->
@if($project->images && count($project->images) > 0)
<section class="project-gallery-section">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="project-gallery">
                    <!-- Main Image -->
                    <div class="main-image-container mb-4">
                        <img id="mainProjectImage" 
                             src="{{ asset('storage/' . $project->images[0]) }}" 
                             alt="{{ $project->title }}" 
                             class="img-fluid rounded shadow-lg w-100"
                             style="height: 500px; object-fit: cover; cursor: pointer;"
                             onclick="openLightbox('{{ asset('storage/' . $project->images[0]) }}', '{{ $project->title }}')">
                    </div>
                    
                    <!-- Thumbnail Images -->
                    @if(count($project->images) > 1)
                        <div class="thumbnail-container">
                            <div class="row g-2">
                                @foreach($project->images as $index => $image)
                                    <div class="col-2">
                                        <img src="{{ asset('storage/' . $image) }}" 
                                             alt="{{ $project->title }} - Image {{ $index + 1 }}" 
                                             class="img-fluid rounded thumbnail-image {{ $index == 0 ? 'active' : '' }}"
                                             style="height: 80px; object-fit: cover; cursor: pointer; border: 3px solid transparent; transition: all 0.3s ease;"
                                             onclick="changeMainImage('{{ asset('storage/' . $image) }}', this)">
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</section>
@endif

<!-- Project Details Section -->
<section class="section-padding">
    <div class="container">
        <div class="row">
            <div class="col-lg-8">
                <div class="project-content">
                    <h2 class="h3 fw-bold mb-4">Project Overview</h2>
                    <div class="content-text">
                        {!! nl2br(e($project->description)) !!}
                    </div>
                    
                    @if($project->client_name)
                        <div class="client-info mt-4 p-4 bg-light rounded">
                            <h5 class="fw-bold mb-2">
                                <i class="fas fa-user me-2 text-primary"></i>Client Information
                            </h5>
                            <p class="mb-0"><strong>Client:</strong> {{ $project->client_name }}</p>
                        </div>
                    @endif
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="project-sidebar">
                    <!-- Project Specifications -->
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-info-circle me-2"></i>Project Details
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="project-specs">
                                @if($project->category)
                                    <div class="spec-item mb-3">
                                        <strong>Category:</strong>
                                        <span class="float-end">{{ $project->category->name }}</span>
                                    </div>
                                @endif
                                
                                @if($project->location)
                                    <div class="spec-item mb-3">
                                        <strong>Location:</strong>
                                        <span class="float-end">{{ $project->location }}</span>
                                    </div>
                                @endif
                                
                                @if($project->start_date)
                                    <div class="spec-item mb-3">
                                        <strong>Start Date:</strong>
                                        <span class="float-end">{{ $project->start_date->format('M d, Y') }}</span>
                                    </div>
                                @endif
                                
                                @if($project->end_date)
                                    <div class="spec-item mb-3">
                                        <strong>End Date:</strong>
                                        <span class="float-end">{{ $project->end_date->format('M d, Y') }}</span>
                                    </div>
                                @endif
                                
                                @if($project->budget)
                                    <div class="spec-item mb-3">
                                        <strong>Budget:</strong>
                                        <span class="float-end">${{ number_format($project->budget) }}</span>
                                    </div>
                                @endif
                                
                                <div class="spec-item">
                                    <strong>Status:</strong>
                                    <span class="float-end">
                                        <span class="badge 
                                            @if($project->status == 'completed') bg-success 
                                            @elseif($project->status == 'in_progress') bg-warning 
                                            @elseif($project->status == 'planning') bg-info 
                                            @else bg-secondary @endif">
                                            {{ ucfirst(str_replace('_', ' ', $project->status)) }}
                                        </span>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Contact CTA -->
                    <div class="card shadow-sm">
                        <div class="card-body text-center">
                            <h5 class="fw-bold mb-3">Interested in Similar Work?</h5>
                            <p class="text-muted mb-4">Contact us to discuss your project requirements and get a free consultation.</p>
                            <div class="d-grid gap-2">
                                <a href="{{ route('contact') }}" class="btn btn-primary">
                                    <i class="fas fa-envelope me-2"></i>Get Free Quote
                                </a>
                                <a href="tel:+1234567890" class="btn btn-outline-primary">
                                    <i class="fas fa-phone me-2"></i>Call Us Now
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Related Projects Section -->
@if($relatedProjects->count() > 0)
<section class="section-padding bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="text-center mb-5">
                    <h2 class="display-6 fw-bold">Related Projects</h2>
                    <p class="lead text-muted">Explore more projects in the same category</p>
                </div>

                <div class="row g-4">
                    @foreach($relatedProjects as $relatedProject)
                        <div class="col-lg-4 col-md-6">
                            <div class="card h-100 project-card">
                                @if($relatedProject->images && count($relatedProject->images) > 0)
                                    <div class="position-relative overflow-hidden">
                                        <img src="{{ asset('storage/' . $relatedProject->images[0]) }}"
                                             class="card-img-top" alt="{{ $relatedProject->title }}"
                                             style="height: 200px; object-fit: cover; transition: transform 0.3s ease;">
                                        @if($relatedProject->status)
                                            <span class="badge position-absolute top-0 end-0 m-3
                                                @if($relatedProject->status == 'completed') bg-success
                                                @elseif($relatedProject->status == 'in_progress') bg-warning
                                                @elseif($relatedProject->status == 'planning') bg-info
                                                @else bg-secondary @endif">
                                                {{ ucfirst(str_replace('_', ' ', $relatedProject->status)) }}
                                            </span>
                                        @endif
                                    </div>
                                @else
                                    <div class="position-relative">
                                        <img src="https://images.unsplash.com/photo-1590725175499-8b8c8b0c8b0c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                                             class="card-img-top" alt="{{ $relatedProject->title }}"
                                             style="height: 200px; object-fit: cover;">
                                        @if($relatedProject->status)
                                            <span class="badge position-absolute top-0 end-0 m-3
                                                @if($relatedProject->status == 'completed') bg-success
                                                @elseif($relatedProject->status == 'in_progress') bg-warning
                                                @elseif($relatedProject->status == 'planning') bg-info
                                                @else bg-secondary @endif">
                                                {{ ucfirst(str_replace('_', ' ', $relatedProject->status)) }}
                                            </span>
                                        @endif
                                    </div>
                                @endif

                                <div class="card-body d-flex flex-column">
                                    <h5 class="card-title">{{ $relatedProject->title }}</h5>
                                    <p class="card-text flex-grow-1">
                                        {{ $relatedProject->short_description ?: Str::limit($relatedProject->description, 100) }}
                                    </p>

                                    <div class="project-meta mb-3">
                                        @if($relatedProject->location)
                                            <small class="text-muted d-block">
                                                <i class="fas fa-map-marker-alt me-1"></i>{{ $relatedProject->location }}
                                            </small>
                                        @endif
                                    </div>
                                </div>

                                <div class="card-footer bg-transparent">
                                    <a href="{{ route('project', $relatedProject->slug) }}" class="btn btn-outline-primary w-100">
                                        <i class="fas fa-eye me-2"></i>View Details
                                    </a>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <div class="text-center mt-4">
                    <a href="{{ route('projects') }}" class="btn btn-primary">View All Projects</a>
                </div>
            </div>
        </div>
    </div>
</section>
@endif

<!-- Lightbox Modal -->
<div class="modal fade" id="lightboxModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered">
        <div class="modal-content bg-transparent border-0">
            <div class="modal-header border-0">
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <img id="lightboxImage" src="" alt="" class="img-fluid">
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function changeMainImage(imageSrc, thumbnail) {
    // Update main image
    document.getElementById('mainProjectImage').src = imageSrc;

    // Update active thumbnail
    document.querySelectorAll('.thumbnail-image').forEach(img => {
        img.classList.remove('active');
    });
    thumbnail.classList.add('active');
}

function openLightbox(imageSrc, title) {
    document.getElementById('lightboxImage').src = imageSrc;
    document.getElementById('lightboxImage').alt = title;
    new bootstrap.Modal(document.getElementById('lightboxModal')).show();
}
</script>
@endpush

@push('styles')
<style>
.project-hero-section {
    padding: 2rem 0;
    background: var(--bg-light);
}

.project-gallery-section {
    padding: 2rem 0;
}

.thumbnail-image.active {
    border-color: var(--primary-color) !important;
}

.thumbnail-image:hover {
    border-color: var(--accent-color) !important;
    opacity: 0.8;
}

.project-card {
    transition: all 0.3s ease;
}

.project-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
}

.project-card:hover .card-img-top {
    transform: scale(1.05);
}

.spec-item {
    border-bottom: 1px solid #eee;
    padding-bottom: 0.5rem;
}

.spec-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.content-text {
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--text-dark);
}

.breadcrumb {
    background: transparent;
    padding: 0;
}

.breadcrumb-item + .breadcrumb-item::before {
    color: var(--text-muted);
}

.breadcrumb-item a {
    color: var(--primary-color);
    text-decoration: none;
}

.breadcrumb-item a:hover {
    text-decoration: underline;
}

#lightboxModal .modal-content {
    background: rgba(0,0,0,0.9) !important;
}

#lightboxModal .modal-body {
    padding: 0;
}

#lightboxModal img {
    max-height: 80vh;
    width: auto;
}
</style>
@endpush
