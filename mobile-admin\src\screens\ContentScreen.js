import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  RefreshControl,
  TextInput,
  Modal,
  ScrollView,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Picker } from '@react-native-picker/picker';
import { apiService } from '../services/apiService';

const ContentScreen = ({ navigation }) => {
  const [content, setContent] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedPage, setSelectedPage] = useState('');
  const [selectedSection, setSelectedSection] = useState('');
  const [modalVisible, setModalVisible] = useState(false);
  const [editingContent, setEditingContent] = useState(null);
  const [saving, setSaving] = useState(false);
  const [formErrors, setFormErrors] = useState({});
  const [formData, setFormData] = useState({
    title: '',
    key: '',
    content: '',
    type: 'text',
    page: 'homepage',
    section: '',
    description: '',
    sort_order: '0',
    is_active: true,
  });

  useEffect(() => {
    loadContent();
  }, [loadContent]);

  const loadContent = useCallback(async () => {
    try {
      setLoading(true);
      const params = {};
      if (searchQuery.trim()) params.search = searchQuery.trim();
      if (selectedPage) params.page = selectedPage;
      if (selectedSection.trim()) params.section = selectedSection.trim();

      const response = await apiService.getContent(params);
      // API service already extracts data in response interceptor
      setContent(response.data || response || []);
    } catch (error) {
      console.error('Error loading content:', error);
      const errorMessage = error.response?.data?.message ||
        error.message ||
        'Failed to load content. Please check your connection and try again.';
      Alert.alert('Error', errorMessage);
      setContent([]);
    } finally {
      setLoading(false);
    }
  }, [searchQuery, selectedPage, selectedSection]);

  const onRefresh = async () => {
    setRefreshing(true);
    await loadContent();
    setRefreshing(false);
  };

  const handleSearch = () => {
    loadContent();
  };

  const clearFilters = () => {
    setSearchQuery('');
    setSelectedPage('');
    setSelectedSection('');
  };

  const validateForm = () => {
    const errors = {};

    if (!formData.title.trim()) {
      errors.title = 'Title is required';
    }

    if (!formData.key.trim()) {
      errors.key = 'Key is required';
    } else if (!/^[a-zA-Z0-9_]+$/.test(formData.key)) {
      errors.key = 'Key can only contain letters, numbers, and underscores';
    }

    if (!formData.page) {
      errors.page = 'Page is required';
    }

    if (!formData.type) {
      errors.type = 'Type is required';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const openAddModal = () => {
    setEditingContent(null);
    setFormErrors({});
    setFormData({
      title: '',
      key: '',
      content: '',
      type: 'text',
      page: 'homepage',
      section: '',
      description: '',
      sort_order: '0',
      is_active: true,
    });
    setModalVisible(true);
  };

  const openEditModal = async (item) => {
    try {
      setSaving(true);
      const response = await apiService.getContentItem(item.id);
      // API service already extracts data in response interceptor
      const contentData = response.data || response;

      setEditingContent(contentData);
      setFormErrors({});
      setFormData({
        title: contentData.title || '',
        key: contentData.key || '',
        content: contentData.content || '',
        type: contentData.type || 'text',
        page: contentData.page || 'homepage',
        section: contentData.section || '',
        description: contentData.description || '',
        sort_order: contentData.sort_order?.toString() || '0',
        is_active: contentData.is_active !== false,
      });
      setModalVisible(true);
    } catch (error) {
      console.error('Error loading content details:', error);
      const errorMessage = error.response?.data?.message ||
        error.message ||
        'Failed to load content details';
      Alert.alert('Error', errorMessage);
    } finally {
      setSaving(false);
    }
  };

  const handleSave = async () => {
    if (!validateForm()) {
      Alert.alert('Validation Error', 'Please fix the errors in the form');
      return;
    }

    try {
      setSaving(true);
      const data = {
        ...formData,
        sort_order: parseInt(formData.sort_order) || 0,
        title: formData.title.trim(),
        key: formData.key.trim(),
        section: formData.section.trim(),
        description: formData.description.trim(),
      };

      if (editingContent) {
        await apiService.updateContent(editingContent.id, data);
        Alert.alert('Success', 'Content updated successfully');
      } else {
        await apiService.createContent(data);
        Alert.alert('Success', 'Content created successfully');
      }

      setModalVisible(false);
      setFormErrors({});
      loadContent();
    } catch (error) {
      console.error('Error saving content:', error);
      const errorMessage = error.response?.data?.message ||
        error.message ||
        'Failed to save content';
      Alert.alert('Error', errorMessage);
    } finally {
      setSaving(false);
    }
  };

  const handleDelete = (item) => {
    Alert.alert(
      'Delete Content',
      `Are you sure you want to delete "${item.title}"?\n\nThis action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await apiService.deleteContent(item.id);
              Alert.alert('Success', 'Content deleted successfully');
              loadContent();
            } catch (error) {
              console.error('Error deleting content:', error);
              const errorMessage = error.response?.data?.message ||
                error.message ||
                'Failed to delete content';
              Alert.alert('Error', errorMessage);
            }
          },
        },
      ]
    );
  };

  const renderContentItem = ({ item }) => (
    <View style={styles.contentItem}>
      <View style={styles.contentHeader}>
        <Text style={styles.contentTitle}>{item.title}</Text>
        <View style={styles.badges}>
          <Text style={[styles.badge, styles.pageBadge]}>{item.page}</Text>
          {item.section && (
            <Text style={[styles.badge, styles.sectionBadge]}>{item.section}</Text>
          )}
          <Text style={[styles.badge, styles.typeBadge]}>{item.type}</Text>
          <Text style={[
            styles.badge,
            item.is_active ? styles.activeBadge : styles.inactiveBadge
          ]}>
            {item.is_active ? 'Active' : 'Inactive'}
          </Text>
        </View>
      </View>

      <Text style={styles.contentKey}>{item.key}</Text>

      {item.description && (
        <Text style={styles.contentDescription}>{item.description}</Text>
      )}

      {item.content && (
        <Text style={styles.contentPreview} numberOfLines={2}>
          {item.content}
        </Text>
      )}

      <View style={styles.contentActions}>
        <TouchableOpacity
          style={[styles.actionButton, styles.editButton]}
          onPress={() => openEditModal(item)}
        >
          <Text style={styles.actionButtonText}>Edit</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.actionButton, styles.deleteButton]}
          onPress={() => handleDelete(item)}
        >
          <Text style={styles.actionButtonText}>Delete</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <Text style={styles.headerTitle}>Website Content</Text>
          <Text style={styles.headerSubtitle}>
            {content.length} item{content.length !== 1 ? 's' : ''}
          </Text>
        </View>
        <TouchableOpacity
          style={[styles.addButton, saving && styles.disabledButton]}
          onPress={openAddModal}
          disabled={saving}
        >
          {saving ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <Text style={styles.addButtonText}>+ Add</Text>
          )}
        </TouchableOpacity>
      </View>

      {/* Filters */}
      <View style={styles.filtersContainer}>
        <TextInput
          style={styles.searchInput}
          placeholder="Search content..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          onSubmitEditing={handleSearch}
        />

        <View style={styles.filterRow}>
          <View style={styles.pickerContainer}>
            <Picker
              selectedValue={selectedPage}
              onValueChange={setSelectedPage}
              style={styles.picker}
            >
              <Picker.Item label="All Pages" value="" />
              <Picker.Item label="Homepage" value="homepage" />
              <Picker.Item label="About" value="about" />
              <Picker.Item label="Contact" value="contact" />
              <Picker.Item label="General" value="general" />
            </Picker>
          </View>

          <TouchableOpacity style={styles.clearButton} onPress={clearFilters}>
            <Text style={styles.clearButtonText}>Clear</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Content List */}
      {loading && content.length === 0 ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007bff" />
          <Text style={styles.loadingText}>Loading content...</Text>
        </View>
      ) : (
        <FlatList
          data={content}
          renderItem={renderContentItem}
          keyExtractor={(item) => item.id.toString()}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          style={styles.list}
          showsVerticalScrollIndicator={false}
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyTitle}>No content found</Text>
              <Text style={styles.emptySubtitle}>
                {searchQuery || selectedPage || selectedSection
                  ? 'Try adjusting your filters'
                  : 'Add your first content item to get started'}
              </Text>
              {!searchQuery && !selectedPage && !selectedSection && (
                <TouchableOpacity style={styles.emptyButton} onPress={openAddModal}>
                  <Text style={styles.emptyButtonText}>Add Content</Text>
                </TouchableOpacity>
              )}
            </View>
          }
        />
      )}

      {/* Add/Edit Modal */}
      <Modal
        visible={modalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <KeyboardAvoidingView
          style={styles.modalContainer}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        >
          <View style={styles.modalHeader}>
            <TouchableOpacity
              onPress={() => setModalVisible(false)}
              disabled={saving}
            >
              <Text style={[styles.modalCancelText, saving && styles.disabledText]}>
                Cancel
              </Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>
              {editingContent ? 'Edit Content' : 'Add Content'}
            </Text>
            <TouchableOpacity
              onPress={handleSave}
              disabled={saving}
            >
              {saving ? (
                <ActivityIndicator size="small" color="#007bff" />
              ) : (
                <Text style={styles.modalSaveText}>Save</Text>
              )}
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent}>
            <View style={styles.formGroup}>
              <Text style={styles.label}>Title *</Text>
              <TextInput
                style={[styles.input, formErrors.title && styles.inputError]}
                value={formData.title}
                onChangeText={(text) => setFormData({ ...formData, title: text })}
                placeholder="Enter title"
              />
              {formErrors.title && (
                <Text style={styles.errorText}>{formErrors.title}</Text>
              )}
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Key *</Text>
              <TextInput
                style={[styles.input, formErrors.key && styles.inputError]}
                value={formData.key}
                onChangeText={(text) => setFormData({ ...formData, key: text })}
                placeholder="e.g., homepage_hero_title"
              />
              {formErrors.key && (
                <Text style={styles.errorText}>{formErrors.key}</Text>
              )}
            </View>

            <View style={styles.formRow}>
              <View style={styles.formGroupHalf}>
                <Text style={styles.label}>Page *</Text>
                <View style={styles.pickerContainer}>
                  <Picker
                    selectedValue={formData.page}
                    onValueChange={(value) => setFormData({ ...formData, page: value })}
                    style={styles.picker}
                  >
                    <Picker.Item label="Homepage" value="homepage" />
                    <Picker.Item label="About" value="about" />
                    <Picker.Item label="Contact" value="contact" />
                    <Picker.Item label="General" value="general" />
                  </Picker>
                </View>
              </View>

              <View style={styles.formGroupHalf}>
                <Text style={styles.label}>Type *</Text>
                <View style={styles.pickerContainer}>
                  <Picker
                    selectedValue={formData.type}
                    onValueChange={(value) => setFormData({ ...formData, type: value })}
                    style={styles.picker}
                  >
                    <Picker.Item label="Text" value="text" />
                    <Picker.Item label="Textarea" value="textarea" />
                    <Picker.Item label="HTML" value="html" />
                    <Picker.Item label="Image" value="image" />
                    <Picker.Item label="JSON" value="json" />
                  </Picker>
                </View>
              </View>
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Section</Text>
              <TextInput
                style={styles.input}
                value={formData.section}
                onChangeText={(text) => setFormData({ ...formData, section: text })}
                placeholder="e.g., hero, services"
              />
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Content</Text>
              <TextInput
                style={[styles.input, styles.textArea]}
                value={formData.content}
                onChangeText={(text) => setFormData({ ...formData, content: text })}
                placeholder="Enter content"
                multiline
                numberOfLines={4}
              />
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Description</Text>
              <TextInput
                style={styles.input}
                value={formData.description}
                onChangeText={(text) => setFormData({ ...formData, description: text })}
                placeholder="Help text for admin users"
              />
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Sort Order</Text>
              <TextInput
                style={styles.input}
                value={formData.sort_order}
                onChangeText={(text) => setFormData({ ...formData, sort_order: text })}
                placeholder="0"
                keyboardType="numeric"
              />
            </View>
          </ScrollView>
        </KeyboardAvoidingView>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerLeft: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  addButton: {
    backgroundColor: '#007bff',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
    minWidth: 60,
    alignItems: 'center',
  },
  addButtonText: {
    color: '#fff',
    fontWeight: '600',
  },
  disabledButton: {
    backgroundColor: '#6c757d',
    opacity: 0.6,
  },
  disabledText: {
    color: '#999',
  },
  filtersContainer: {
    backgroundColor: '#fff',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  searchInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 6,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginBottom: 12,
    backgroundColor: '#fff',
  },
  filterRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  pickerContainer: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 6,
    backgroundColor: '#fff',
    marginRight: 12,
  },
  picker: {
    height: 40,
  },
  clearButton: {
    backgroundColor: '#6c757d',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
  },
  clearButtonText: {
    color: '#fff',
    fontWeight: '600',
  },
  list: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#666',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
    marginTop: 60,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 24,
  },
  emptyButton: {
    backgroundColor: '#007bff',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 6,
  },
  emptyButtonText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 16,
  },
  contentItem: {
    backgroundColor: '#fff',
    margin: 8,
    padding: 16,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  contentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  contentTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
    marginRight: 8,
  },
  badges: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  badge: {
    fontSize: 10,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    marginLeft: 4,
    marginBottom: 2,
    color: '#fff',
    fontWeight: '600',
  },
  pageBadge: {
    backgroundColor: '#17a2b8',
  },
  sectionBadge: {
    backgroundColor: '#6c757d',
  },
  typeBadge: {
    backgroundColor: '#007bff',
  },
  activeBadge: {
    backgroundColor: '#28a745',
  },
  inactiveBadge: {
    backgroundColor: '#dc3545',
  },
  contentKey: {
    fontSize: 12,
    color: '#666',
    fontFamily: 'monospace',
    marginBottom: 4,
  },
  contentDescription: {
    fontSize: 12,
    color: '#888',
    marginBottom: 8,
  },
  contentPreview: {
    fontSize: 14,
    color: '#555',
    marginBottom: 12,
  },
  contentActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  actionButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
    marginLeft: 8,
  },
  editButton: {
    backgroundColor: '#007bff',
  },
  deleteButton: {
    backgroundColor: '#dc3545',
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#fff',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  modalCancelText: {
    color: '#007bff',
    fontSize: 16,
  },
  modalSaveText: {
    color: '#007bff',
    fontSize: 16,
    fontWeight: '600',
  },
  modalContent: {
    flex: 1,
    padding: 16,
  },
  formGroup: {
    marginBottom: 16,
  },
  formRow: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  formGroupHalf: {
    flex: 1,
    marginRight: 8,
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 6,
    paddingHorizontal: 12,
    paddingVertical: 8,
    backgroundColor: '#fff',
    fontSize: 14,
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  inputError: {
    borderColor: '#dc3545',
    borderWidth: 2,
  },
  errorText: {
    color: '#dc3545',
    fontSize: 12,
    marginTop: 4,
  },
});

export default ContentScreen;
