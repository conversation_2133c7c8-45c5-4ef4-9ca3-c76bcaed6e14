@extends('layouts.app')

@section('title', 'Our Projects - Construction Portfolio')
@section('description', 'Explore our portfolio of completed construction projects including residential homes, commercial buildings, and renovation projects.')

@section('content')
<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h1 class="display-4 fw-bold mb-4">Our Projects</h1>
                <p class="lead">Discover our portfolio of successful construction projects that showcase our expertise and commitment to quality.</p>
            </div>
        </div>
    </div>
</section>

<!-- Projects Section -->
<section class="section-padding">
    <div class="container">
        <!-- Filter Options -->
        <div class="row mb-5">
            <div class="col-lg-8 mx-auto">
                <div class="card shadow-sm">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h5 class="mb-0">Filter Projects</h5>
                            </div>
                            <div class="col-md-6">
                                <form method="GET" action="{{ route('projects') }}">
                                    <div class="input-group">
                                        <select name="category" class="form-select">
                                            <option value="">All Categories</option>
                                            <option value="residential" {{ request('category') == 'residential' ? 'selected' : '' }}>Residential</option>
                                            <option value="commercial" {{ request('category') == 'commercial' ? 'selected' : '' }}>Commercial</option>
                                            <option value="renovation" {{ request('category') == 'renovation' ? 'selected' : '' }}>Renovation</option>
                                            <option value="infrastructure" {{ request('category') == 'infrastructure' ? 'selected' : '' }}>Infrastructure</option>
                                        </select>
                                        <button class="btn btn-primary" type="submit">
                                            <i class="fas fa-filter me-1"></i>Filter
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Projects Grid -->
        <div class="row g-4">
            @forelse($projects as $project)
            <div class="col-lg-4 col-md-6">
                <div class="card h-100 project-card">
                    @if($project->images && count($project->images) > 0)
                        <div class="position-relative overflow-hidden">
                            <img src="{{ asset('storage/' . $project->images[0]) }}" 
                                 class="card-img-top" alt="{{ $project->title }}" 
                                 style="height: 250px; object-fit: cover; transition: transform 0.3s ease;">
                            @if($project->status)
                                <span class="badge position-absolute top-0 end-0 m-3 
                                    @if($project->status == 'completed') bg-success 
                                    @elseif($project->status == 'in_progress') bg-warning 
                                    @elseif($project->status == 'planning') bg-info 
                                    @else bg-secondary @endif">
                                    {{ ucfirst(str_replace('_', ' ', $project->status)) }}
                                </span>
                            @endif
                        </div>
                    @else
                        <div class="position-relative">
                            <img src="https://images.unsplash.com/photo-1590725175499-8b8c8b0c8b0c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" 
                                 class="card-img-top" alt="{{ $project->title }}" 
                                 style="height: 250px; object-fit: cover;">
                            @if($project->status)
                                <span class="badge position-absolute top-0 end-0 m-3 
                                    @if($project->status == 'completed') bg-success 
                                    @elseif($project->status == 'in_progress') bg-warning 
                                    @elseif($project->status == 'planning') bg-info 
                                    @else bg-secondary @endif">
                                    {{ ucfirst(str_replace('_', ' ', $project->status)) }}
                                </span>
                            @endif
                        </div>
                    @endif
                    
                    <div class="card-body d-flex flex-column">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h5 class="card-title">{{ $project->title }}</h5>
                            @if($project->category)
                                <span class="badge bg-primary">{{ $project->category->name }}</span>
                            @endif
                        </div>
                        
                        <p class="card-text flex-grow-1">
                            {{ $project->short_description ?: Str::limit($project->description, 120) }}
                        </p>
                        
                        <div class="project-meta mb-3">
                            @if($project->location)
                                <small class="text-muted d-block">
                                    <i class="fas fa-map-marker-alt me-1"></i>{{ $project->location }}
                                </small>
                            @endif
                            
                            @if($project->start_date)
                                <small class="text-muted d-block">
                                    <i class="fas fa-calendar me-1"></i>{{ $project->start_date->format('M Y') }}
                                    @if($project->end_date)
                                        - {{ $project->end_date->format('M Y') }}
                                    @endif
                                </small>
                            @endif
                            
                            @if($project->budget)
                                <small class="text-muted d-block">
                                    <i class="fas fa-dollar-sign me-1"></i>Budget: ${{ number_format($project->budget) }}
                                </small>
                            @endif
                        </div>
                    </div>
                    
                    <div class="card-footer bg-transparent">
                        <a href="{{ route('project', $project->slug) }}" class="btn btn-primary w-100">
                            <i class="fas fa-eye me-2"></i>View Details
                        </a>
                    </div>
                </div>
            </div>
            @empty
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-hard-hat fa-4x text-muted mb-3"></i>
                    <h3 class="text-muted">No Projects Found</h3>
                    <p class="text-muted">We haven't added any projects yet, or no projects match your current filter.</p>
                    @if(request('category'))
                        <a href="{{ route('projects') }}" class="btn btn-primary">View All Projects</a>
                    @endif
                </div>
            </div>
            @endforelse
        </div>

        <!-- Pagination -->
        @if($projects->hasPages())
        <div class="row mt-5">
            <div class="col-12">
                <nav aria-label="Projects pagination">
                    {{ $projects->links() }}
                </nav>
            </div>
        </div>
        @endif
    </div>
</section>

<!-- Call to Action Section -->
<section class="section-padding bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h2 class="display-6 fw-bold mb-3">Ready to Start Your Project?</h2>
                <p class="lead mb-4">Let us help you bring your construction vision to life. Contact us today for a free consultation and quote.</p>
                <div class="d-flex flex-wrap justify-content-center gap-3">
                    <a href="{{ route('contact') }}" class="btn btn-primary btn-lg">
                        <i class="fas fa-envelope me-2"></i>Get Free Quote
                    </a>
                    <a href="tel:+1234567890" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-phone me-2"></i>Call Us Now
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@push('styles')
<style>
.project-card {
    transition: all 0.3s ease;
}

.project-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
}

.project-card:hover .card-img-top {
    transform: scale(1.05);
}

.project-meta small {
    line-height: 1.6;
}

.badge {
    font-size: 0.75rem;
}
</style>
@endpush
