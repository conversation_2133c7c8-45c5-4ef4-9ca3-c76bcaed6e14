<?php

use App\Http\Controllers\HomeController;
use App\Http\Controllers\Admin\AdminController;
use Illuminate\Support\Facades\Route;

// Frontend Routes
Route::get('/', [HomeController::class, 'index'])->name('home');
Route::get('/about', [HomeController::class, 'about'])->name('about');
Route::get('/projects', [HomeController::class, 'projects'])->name('projects');
Route::get('/projects/{project:slug}', [HomeController::class, 'project'])->name('project');
Route::get('/services', [HomeController::class, 'services'])->name('services');
Route::get('/services/{service:slug}', [HomeController::class, 'service'])->name('service');
Route::get('/media', [HomeController::class, 'media'])->name('media');
Route::get('/contact', [HomeController::class, 'contact'])->name('contact');
Route::post('/contact', [HomeController::class, 'contactSubmit'])->name('contact.submit');

// Admin Routes (Web-based admin panel)
Route::prefix('admin')->name('admin.')->group(function () {
    Route::get('/login', [AdminController::class, 'loginForm'])->name('login');
    Route::post('/login', [AdminController::class, 'login'])->name('login.submit');

    Route::middleware('auth')->group(function () {
        Route::get('/', [AdminController::class, 'dashboard'])->name('dashboard');

        // Projects routes
        Route::get('/projects', [AdminController::class, 'projects'])->name('projects');
        Route::post('/projects', [AdminController::class, 'storeProject'])->name('projects.store');
        Route::get('/projects/{project}/edit', [AdminController::class, 'editProject'])->name('projects.edit');
        Route::post('/projects/{project}', [AdminController::class, 'updateProject'])->name('projects.update');
        Route::delete('/projects/{project}', [AdminController::class, 'destroyProject'])->name('projects.destroy');
        Route::post('/projects/{project}/remove-image', [AdminController::class, 'removeProjectImage'])->name('projects.remove-image');

        // Services routes
        Route::get('/services', [AdminController::class, 'services'])->name('services');
        Route::post('/services', [AdminController::class, 'storeService'])->name('services.store');
        Route::get('/services/{service}/edit', [AdminController::class, 'editService'])->name('services.edit');
        Route::post('/services/{service}', [AdminController::class, 'updateService'])->name('services.update');
        Route::delete('/services/{service}', [AdminController::class, 'destroyService'])->name('services.destroy');
        Route::post('/services/{service}/remove-image', [AdminController::class, 'removeServiceImage'])->name('services.remove-image');

        // Media routes
        Route::get('/media', [AdminController::class, 'media'])->name('media');
        Route::post('/media', [AdminController::class, 'storeMedia'])->name('media.store');
        Route::delete('/media/{media}', [AdminController::class, 'destroyMedia'])->name('media.destroy');

        // Messages routes
        Route::get('/messages', [AdminController::class, 'messages'])->name('messages');
        Route::get('/messages/{message}', [AdminController::class, 'showMessage'])->name('messages.show');
        Route::put('/messages/{message}', [AdminController::class, 'updateMessage'])->name('messages.update');
        Route::delete('/messages/{message}', [AdminController::class, 'destroyMessage'])->name('messages.destroy');
        Route::post('/messages/bulk-delete', [AdminController::class, 'bulkDeleteMessages'])->name('messages.bulk-delete');
        Route::post('/messages/mark-all-read', [AdminController::class, 'markAllMessagesRead'])->name('messages.mark-all-read');

        // Content management routes
        Route::get('/content', [AdminController::class, 'content'])->name('content');
        Route::post('/content', [AdminController::class, 'storeContent'])->name('content.store');
        Route::get('/content/{content}/edit', [AdminController::class, 'editContent'])->name('content.edit');
        Route::post('/content/{content}', [AdminController::class, 'updateContent'])->name('content.update');
        Route::delete('/content/{content}', [AdminController::class, 'destroyContent'])->name('content.destroy');
        Route::post('/content/bulk-update', [AdminController::class, 'bulkUpdateContent'])->name('content.bulk-update');

        // Settings routes
        Route::get('/settings', [AdminController::class, 'settings'])->name('settings');
        Route::post('/settings', [AdminController::class, 'updateSettings'])->name('settings.update');
        Route::post('/settings/store', [AdminController::class, 'storeSetting'])->name('settings.store');
        Route::delete('/settings/{setting}', [AdminController::class, 'destroySetting'])->name('settings.destroy');

        // Profile routes
        Route::get('/profile', [AdminController::class, 'profile'])->name('profile');
        Route::post('/profile', [AdminController::class, 'updateProfile'])->name('profile.update');
        Route::post('/profile/password', [AdminController::class, 'updatePassword'])->name('profile.password');
        Route::post('/profile/preferences', [AdminController::class, 'updatePreferences'])->name('profile.preferences');
        Route::delete('/profile/avatar', [AdminController::class, 'removeAvatar'])->name('profile.avatar.remove');

        Route::post('/logout', [AdminController::class, 'logout'])->name('logout');
    });
});
