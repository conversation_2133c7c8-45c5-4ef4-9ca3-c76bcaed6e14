<?php $__env->startSection('title', $project->title . ' - Project Details'); ?>
<?php $__env->startSection('description', $project->short_description ?: Str::limit($project->description, 160)); ?>

<?php $__env->startSection('content'); ?>
<!-- Project Hero Section -->
<section class="project-hero-section">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <nav aria-label="breadcrumb" class="mb-4">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('home')); ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?php echo e(route('projects')); ?>">Projects</a></li>
                        <li class="breadcrumb-item active" aria-current="page"><?php echo e($project->title); ?></li>
                    </ol>
                </nav>
                
                <div class="row align-items-center">
                    <div class="col-lg-8">
                        <div class="d-flex align-items-center mb-3">
                            <h1 class="display-5 fw-bold mb-0 me-3"><?php echo e($project->title); ?></h1>
                            <?php if($project->status): ?>
                                <span class="badge fs-6 
                                    <?php if($project->status == 'completed'): ?> bg-success 
                                    <?php elseif($project->status == 'in_progress'): ?> bg-warning 
                                    <?php elseif($project->status == 'planning'): ?> bg-info 
                                    <?php else: ?> bg-secondary <?php endif; ?>">
                                    <?php echo e(ucfirst(str_replace('_', ' ', $project->status))); ?>

                                </span>
                            <?php endif; ?>
                        </div>
                        
                        <?php if($project->short_description): ?>
                            <p class="lead text-muted"><?php echo e($project->short_description); ?></p>
                        <?php endif; ?>
                        
                        <div class="project-quick-info">
                            <div class="row g-3">
                                <?php if($project->category): ?>
                                    <div class="col-auto">
                                        <span class="badge bg-primary fs-6">
                                            <i class="fas fa-tag me-1"></i><?php echo e($project->category->name); ?>

                                        </span>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if($project->location): ?>
                                    <div class="col-auto">
                                        <span class="text-muted">
                                            <i class="fas fa-map-marker-alt me-1"></i><?php echo e($project->location); ?>

                                        </span>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if($project->start_date): ?>
                                    <div class="col-auto">
                                        <span class="text-muted">
                                            <i class="fas fa-calendar me-1"></i>
                                            <?php echo e($project->start_date->format('M Y')); ?>

                                            <?php if($project->end_date): ?>
                                                - <?php echo e($project->end_date->format('M Y')); ?>

                                            <?php endif; ?>
                                        </span>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-4 text-lg-end">
                        <?php if($project->budget): ?>
                            <div class="project-budget">
                                <h5 class="text-muted mb-1">Project Budget</h5>
                                <h3 class="fw-bold text-primary">$<?php echo e(number_format($project->budget)); ?></h3>
                            </div>
                        <?php endif; ?>
                        
                        <?php if($project->progress && $project->status == 'in_progress'): ?>
                            <div class="project-progress mt-3">
                                <h6 class="text-muted mb-2">Progress</h6>
                                <div class="progress" style="height: 10px;">
                                    <div class="progress-bar bg-warning" role="progressbar" 
                                         style="width: <?php echo e($project->progress); ?>%" 
                                         aria-valuenow="<?php echo e($project->progress); ?>" 
                                         aria-valuemin="0" aria-valuemax="100">
                                    </div>
                                </div>
                                <small class="text-muted"><?php echo e($project->progress); ?>% Complete</small>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Project Images Gallery -->
<?php if($project->images && count($project->images) > 0): ?>
<section class="project-gallery-section">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="project-gallery">
                    <!-- Main Image -->
                    <div class="main-image-container mb-4">
                        <img id="mainProjectImage" 
                             src="<?php echo e(asset('storage/' . $project->images[0])); ?>" 
                             alt="<?php echo e($project->title); ?>" 
                             class="img-fluid rounded shadow-lg w-100"
                             style="height: 500px; object-fit: cover; cursor: pointer;"
                             onclick="openLightbox('<?php echo e(asset('storage/' . $project->images[0])); ?>', '<?php echo e($project->title); ?>')">
                    </div>
                    
                    <!-- Thumbnail Images -->
                    <?php if(count($project->images) > 1): ?>
                        <div class="thumbnail-container">
                            <div class="row g-2">
                                <?php $__currentLoopData = $project->images; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="col-2">
                                        <img src="<?php echo e(asset('storage/' . $image)); ?>" 
                                             alt="<?php echo e($project->title); ?> - Image <?php echo e($index + 1); ?>" 
                                             class="img-fluid rounded thumbnail-image <?php echo e($index == 0 ? 'active' : ''); ?>"
                                             style="height: 80px; object-fit: cover; cursor: pointer; border: 3px solid transparent; transition: all 0.3s ease;"
                                             onclick="changeMainImage('<?php echo e(asset('storage/' . $image)); ?>', this)">
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Project Details Section -->
<section class="section-padding">
    <div class="container">
        <div class="row">
            <div class="col-lg-8">
                <div class="project-content">
                    <h2 class="h3 fw-bold mb-4">Project Overview</h2>
                    <div class="content-text">
                        <?php echo nl2br(e($project->description)); ?>

                    </div>
                    
                    <?php if($project->client_name): ?>
                        <div class="client-info mt-4 p-4 bg-light rounded">
                            <h5 class="fw-bold mb-2">
                                <i class="fas fa-user me-2 text-primary"></i>Client Information
                            </h5>
                            <p class="mb-0"><strong>Client:</strong> <?php echo e($project->client_name); ?></p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="project-sidebar">
                    <!-- Project Specifications -->
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-info-circle me-2"></i>Project Details
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="project-specs">
                                <?php if($project->category): ?>
                                    <div class="spec-item mb-3">
                                        <strong>Category:</strong>
                                        <span class="float-end"><?php echo e($project->category->name); ?></span>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if($project->location): ?>
                                    <div class="spec-item mb-3">
                                        <strong>Location:</strong>
                                        <span class="float-end"><?php echo e($project->location); ?></span>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if($project->start_date): ?>
                                    <div class="spec-item mb-3">
                                        <strong>Start Date:</strong>
                                        <span class="float-end"><?php echo e($project->start_date->format('M d, Y')); ?></span>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if($project->end_date): ?>
                                    <div class="spec-item mb-3">
                                        <strong>End Date:</strong>
                                        <span class="float-end"><?php echo e($project->end_date->format('M d, Y')); ?></span>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if($project->budget): ?>
                                    <div class="spec-item mb-3">
                                        <strong>Budget:</strong>
                                        <span class="float-end">$<?php echo e(number_format($project->budget)); ?></span>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="spec-item">
                                    <strong>Status:</strong>
                                    <span class="float-end">
                                        <span class="badge 
                                            <?php if($project->status == 'completed'): ?> bg-success 
                                            <?php elseif($project->status == 'in_progress'): ?> bg-warning 
                                            <?php elseif($project->status == 'planning'): ?> bg-info 
                                            <?php else: ?> bg-secondary <?php endif; ?>">
                                            <?php echo e(ucfirst(str_replace('_', ' ', $project->status))); ?>

                                        </span>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Contact CTA -->
                    <div class="card shadow-sm">
                        <div class="card-body text-center">
                            <h5 class="fw-bold mb-3">Interested in Similar Work?</h5>
                            <p class="text-muted mb-4">Contact us to discuss your project requirements and get a free consultation.</p>
                            <div class="d-grid gap-2">
                                <a href="<?php echo e(route('contact')); ?>" class="btn btn-primary">
                                    <i class="fas fa-envelope me-2"></i>Get Free Quote
                                </a>
                                <a href="tel:+1234567890" class="btn btn-outline-primary">
                                    <i class="fas fa-phone me-2"></i>Call Us Now
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Related Projects Section -->
<?php if($relatedProjects->count() > 0): ?>
<section class="section-padding bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="text-center mb-5">
                    <h2 class="display-6 fw-bold">Related Projects</h2>
                    <p class="lead text-muted">Explore more projects in the same category</p>
                </div>

                <div class="row g-4">
                    <?php $__currentLoopData = $relatedProjects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $relatedProject): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="col-lg-4 col-md-6">
                            <div class="card h-100 project-card">
                                <?php if($relatedProject->images && count($relatedProject->images) > 0): ?>
                                    <div class="position-relative overflow-hidden">
                                        <img src="<?php echo e(asset('storage/' . $relatedProject->images[0])); ?>"
                                             class="card-img-top" alt="<?php echo e($relatedProject->title); ?>"
                                             style="height: 200px; object-fit: cover; transition: transform 0.3s ease;">
                                        <?php if($relatedProject->status): ?>
                                            <span class="badge position-absolute top-0 end-0 m-3
                                                <?php if($relatedProject->status == 'completed'): ?> bg-success
                                                <?php elseif($relatedProject->status == 'in_progress'): ?> bg-warning
                                                <?php elseif($relatedProject->status == 'planning'): ?> bg-info
                                                <?php else: ?> bg-secondary <?php endif; ?>">
                                                <?php echo e(ucfirst(str_replace('_', ' ', $relatedProject->status))); ?>

                                            </span>
                                        <?php endif; ?>
                                    </div>
                                <?php else: ?>
                                    <div class="position-relative">
                                        <img src="https://images.unsplash.com/photo-1590725175499-8b8c8b0c8b0c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                                             class="card-img-top" alt="<?php echo e($relatedProject->title); ?>"
                                             style="height: 200px; object-fit: cover;">
                                        <?php if($relatedProject->status): ?>
                                            <span class="badge position-absolute top-0 end-0 m-3
                                                <?php if($relatedProject->status == 'completed'): ?> bg-success
                                                <?php elseif($relatedProject->status == 'in_progress'): ?> bg-warning
                                                <?php elseif($relatedProject->status == 'planning'): ?> bg-info
                                                <?php else: ?> bg-secondary <?php endif; ?>">
                                                <?php echo e(ucfirst(str_replace('_', ' ', $relatedProject->status))); ?>

                                            </span>
                                        <?php endif; ?>
                                    </div>
                                <?php endif; ?>

                                <div class="card-body d-flex flex-column">
                                    <h5 class="card-title"><?php echo e($relatedProject->title); ?></h5>
                                    <p class="card-text flex-grow-1">
                                        <?php echo e($relatedProject->short_description ?: Str::limit($relatedProject->description, 100)); ?>

                                    </p>

                                    <div class="project-meta mb-3">
                                        <?php if($relatedProject->location): ?>
                                            <small class="text-muted d-block">
                                                <i class="fas fa-map-marker-alt me-1"></i><?php echo e($relatedProject->location); ?>

                                            </small>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <div class="card-footer bg-transparent">
                                    <a href="<?php echo e(route('project', $relatedProject->slug)); ?>" class="btn btn-outline-primary w-100">
                                        <i class="fas fa-eye me-2"></i>View Details
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                <div class="text-center mt-4">
                    <a href="<?php echo e(route('projects')); ?>" class="btn btn-primary">View All Projects</a>
                </div>
            </div>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Lightbox Modal -->
<div class="modal fade" id="lightboxModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered">
        <div class="modal-content bg-transparent border-0">
            <div class="modal-header border-0">
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <img id="lightboxImage" src="" alt="" class="img-fluid">
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
function changeMainImage(imageSrc, thumbnail) {
    // Update main image
    document.getElementById('mainProjectImage').src = imageSrc;

    // Update active thumbnail
    document.querySelectorAll('.thumbnail-image').forEach(img => {
        img.classList.remove('active');
    });
    thumbnail.classList.add('active');
}

function openLightbox(imageSrc, title) {
    document.getElementById('lightboxImage').src = imageSrc;
    document.getElementById('lightboxImage').alt = title;
    new bootstrap.Modal(document.getElementById('lightboxModal')).show();
}
</script>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('styles'); ?>
<style>
.project-hero-section {
    padding: 2rem 0;
    background: var(--bg-light);
}

.project-gallery-section {
    padding: 2rem 0;
}

.thumbnail-image.active {
    border-color: var(--primary-color) !important;
}

.thumbnail-image:hover {
    border-color: var(--accent-color) !important;
    opacity: 0.8;
}

.project-card {
    transition: all 0.3s ease;
}

.project-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
}

.project-card:hover .card-img-top {
    transform: scale(1.05);
}

.spec-item {
    border-bottom: 1px solid #eee;
    padding-bottom: 0.5rem;
}

.spec-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.content-text {
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--text-dark);
}

.breadcrumb {
    background: transparent;
    padding: 0;
}

.breadcrumb-item + .breadcrumb-item::before {
    color: var(--text-muted);
}

.breadcrumb-item a {
    color: var(--primary-color);
    text-decoration: none;
}

.breadcrumb-item a:hover {
    text-decoration: underline;
}

#lightboxModal .modal-content {
    background: rgba(0,0,0,0.9) !important;
}

#lightboxModal .modal-body {
    padding: 0;
}

#lightboxModal img {
    max-height: 80vh;
    width: auto;
}
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\new-project-app\resources\views/project.blade.php ENDPATH**/ ?>