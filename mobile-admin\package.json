{"name": "mobile-admin", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "eject": "expo eject", "test": "jest"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-picker/picker": "^2.11.0", "@react-navigation/bottom-tabs": "^7.3.14", "@react-navigation/native": "^7.1.10", "@react-navigation/stack": "^7.3.3", "axios": "^1.9.0", "expo": "~53.0.9", "expo-blur": "~13.0.2", "expo-document-picker": "^13.1.5", "expo-image-picker": "^16.1.4", "expo-linear-gradient": "~13.0.2", "expo-secure-store": "^14.2.3", "expo-status-bar": "~2.2.3", "lottie-react-native": "7.1.0", "react": "19.0.0", "react-native": "0.79.2", "react-native-elements": "^3.4.3", "react-native-gesture-handler": "~2.20.2", "react-native-paper": "^5.12.3", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.14.0", "react-native-screens": "4.1.0", "react-native-super-grid": "^6.0.1", "react-native-svg": "15.8.0", "react-native-vector-icons": "^10.2.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^13.2.0", "jest": "^29.2.1", "jest-expo": "~53.0.0", "react-test-renderer": "19.0.0"}, "jest": {"preset": "jest-expo"}, "private": true}