<?php

use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\ProjectController;
use App\Http\Controllers\Api\ServiceController;
use App\Http\Controllers\Api\MediaController;
use App\Http\Controllers\Api\MessageController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Public routes
Route::post('/login', [AuthController::class, 'login']);
Route::post('/register', [AuthController::class, 'register']);

// Public routes for frontend
Route::get('/projects', [ProjectController::class, 'index']);
Route::get('/projects/{project}', [ProjectController::class, 'show']);
Route::get('/services', [ServiceController::class, 'index']);
Route::get('/services/{service}', [ServiceController::class, 'show']);
Route::get('/media', [MediaController::class, 'index']);
Route::post('/messages', [MessageController::class, 'store']); // Contact form
Route::get('/categories', [\App\Http\Controllers\Api\CategoryController::class, 'index']);

// Protected routes (require authentication)
Route::middleware('auth:sanctum')->group(function () {
    // Auth routes
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::get('/user', [AuthController::class, 'user']);
    
    // Admin routes for projects
    Route::post('/projects', [ProjectController::class, 'store']);
    Route::get('/projects/{project}/edit', [ProjectController::class, 'edit']);
    Route::put('/projects/{project}', [ProjectController::class, 'update']);
    Route::post('/projects/{project}', [ProjectController::class, 'update']); // For FormData uploads
    Route::delete('/projects/{project}', [ProjectController::class, 'destroy']);

    // Admin routes for services
    Route::post('/services', [ServiceController::class, 'store']);
    Route::get('/services/{service}/edit', [ServiceController::class, 'edit']);
    Route::put('/services/{service}', [ServiceController::class, 'update']);
    Route::post('/services/{service}', [ServiceController::class, 'update']); // For FormData uploads
    Route::delete('/services/{service}', [ServiceController::class, 'destroy']);
    
    // Admin routes for media
    Route::post('/media', [MediaController::class, 'store']);
    Route::put('/media/{media}', [MediaController::class, 'update']);
    Route::delete('/media/{media}', [MediaController::class, 'destroy']);

    // Admin routes for content
    Route::get('/content', [AdminController::class, 'content']);
    Route::post('/content', [AdminController::class, 'storeContent']);
    Route::get('/content/{content}/edit', [AdminController::class, 'editContent']);
    Route::put('/content/{content}', [AdminController::class, 'updateContent']);
    Route::post('/content/{content}', [AdminController::class, 'updateContent']); // For FormData uploads
    Route::delete('/content/{content}', [AdminController::class, 'destroyContent']);
    Route::post('/content/bulk-update', [AdminController::class, 'bulkUpdateContent']);

    // Admin routes for settings
    Route::get('/settings', [AdminController::class, 'settings']);
    Route::post('/settings', [AdminController::class, 'updateSettings']);
    Route::post('/settings/store', [AdminController::class, 'storeSetting']);
    Route::delete('/settings/{setting}', [AdminController::class, 'destroySetting']);

    // Admin routes for profile
    Route::get('/profile', [AdminController::class, 'profile']);
    Route::post('/profile', [AdminController::class, 'updateProfile']);
    Route::post('/profile/password', [AdminController::class, 'updatePassword']);
    Route::post('/profile/preferences', [AdminController::class, 'updatePreferences']);
    Route::delete('/profile/avatar', [AdminController::class, 'removeAvatar']);
    
    // Admin routes for messages
    Route::get('/messages', [MessageController::class, 'index']);
    Route::get('/messages/{message}', [MessageController::class, 'show']);
    Route::put('/messages/{message}', [MessageController::class, 'update']);
    Route::delete('/messages/{message}', [MessageController::class, 'destroy']);
    Route::post('/messages/bulk-delete', [MessageController::class, 'bulkDelete']);
    Route::post('/messages/mark-all-read', [MessageController::class, 'markAllRead']);
    
    // Dashboard stats
    Route::get('/dashboard/stats', function () {
        return response()->json([
            'projects' => \App\Models\Project::count(),
            'services' => \App\Models\Service::count(),
            'media' => \App\Models\Media::count(),
            'messages' => \App\Models\Message::where('status', 'new')->count(),
            'total_messages' => \App\Models\Message::count(),
        ]);
    });
});
