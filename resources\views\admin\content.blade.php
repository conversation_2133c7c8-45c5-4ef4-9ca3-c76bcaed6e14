@extends('layouts.admin')

@section('page-title', 'Website Content Management')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Website Content</h5>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addContentModal">
                        <i class="fas fa-plus me-2"></i>Add Content
                    </button>
                </div>
                
                <div class="card-body">
                    <!-- Filters -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <input type="text" class="form-control" placeholder="Search content..." id="searchInput" value="{{ request('search') }}">
                        </div>
                        <div class="col-md-2">
                            <select class="form-select" id="pageFilter">
                                <option value="">All Pages</option>
                                @foreach($pages as $page)
                                    <option value="{{ $page }}" {{ request('page') == $page ? 'selected' : '' }}>
                                        {{ ucfirst($page) }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-2">
                            <select class="form-select" id="sectionFilter">
                                <option value="">All Sections</option>
                                @foreach($sections as $section)
                                    <option value="{{ $section }}" {{ request('section') == $section ? 'selected' : '' }}>
                                        {{ ucfirst($section) }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-2">
                            <button type="button" class="btn btn-outline-secondary" onclick="clearFilters()">
                                <i class="fas fa-times me-1"></i>Clear
                            </button>
                        </div>
                    </div>

                    <!-- Content Table -->
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Title</th>
                                    <th>Key</th>
                                    <th>Page</th>
                                    <th>Section</th>
                                    <th>Type</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($contents as $content)
                                <tr>
                                    <td>
                                        <strong>{{ $content->title }}</strong>
                                        @if($content->description)
                                            <br><small class="text-muted">{{ Str::limit($content->description, 50) }}</small>
                                        @endif
                                    </td>
                                    <td><code>{{ $content->key }}</code></td>
                                    <td><span class="badge bg-info">{{ $content->page }}</span></td>
                                    <td>
                                        @if($content->section)
                                            <span class="badge bg-secondary">{{ $content->section }}</span>
                                        @else
                                            <span class="text-muted">-</span>
                                        @endif
                                    </td>
                                    <td><span class="badge bg-primary">{{ $content->type }}</span></td>
                                    <td>
                                        @if($content->is_active)
                                            <span class="badge bg-success">Active</span>
                                        @else
                                            <span class="badge bg-danger">Inactive</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="editContent({{ $content->id }})">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteContent({{ $content->id }})">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="7" class="text-center text-muted py-4">
                                        <i class="fas fa-inbox fa-3x mb-3"></i>
                                        <p>No content found. <a href="#" data-bs-toggle="modal" data-bs-target="#addContentModal">Add your first content item</a>.</p>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if($contents->hasPages())
                        <div class="d-flex justify-content-center">
                            {{ $contents->appends(request()->query())->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Content Modal -->
<div class="modal fade" id="addContentModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Content</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addContentForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="add_title" class="form-label">Title *</label>
                                <input type="text" class="form-control" id="add_title" name="title" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="add_key" class="form-label">Key *</label>
                                <input type="text" class="form-control" id="add_key" name="key" required>
                                <div class="form-text">Unique identifier (e.g., homepage_hero_title)</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="add_page" class="form-label">Page *</label>
                                <select class="form-select" id="add_page" name="page" required>
                                    <option value="">Select Page</option>
                                    <option value="homepage">Homepage</option>
                                    <option value="about">About</option>
                                    <option value="contact">Contact</option>
                                    <option value="general">General</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="add_section" class="form-label">Section</label>
                                <input type="text" class="form-control" id="add_section" name="section" placeholder="e.g., hero, services">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="add_type" class="form-label">Type *</label>
                                <select class="form-select" id="add_type" name="type" required>
                                    <option value="text">Text</option>
                                    <option value="textarea">Textarea</option>
                                    <option value="html">HTML</option>
                                    <option value="image">Image</option>
                                    <option value="json">JSON</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="add_content" class="form-label">Content</label>
                        <textarea class="form-control" id="add_content" name="content" rows="4"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="add_description" class="form-label">Description</label>
                        <input type="text" class="form-control" id="add_description" name="description" placeholder="Help text for admin users">
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="add_sort_order" class="form-label">Sort Order</label>
                                <input type="number" class="form-control" id="add_sort_order" name="sort_order" value="0" min="0">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="add_is_active" name="is_active" checked>
                                    <label class="form-check-label" for="add_is_active">
                                        Active
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Content</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Content Modal -->
<div class="modal fade" id="editContentModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Content</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editContentForm">
                <input type="hidden" id="edit_content_id" name="content_id">
                <div class="modal-body">
                    <!-- Same form fields as add modal but with edit_ prefixes -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_title" class="form-label">Title *</label>
                                <input type="text" class="form-control" id="edit_title" name="title" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_key" class="form-label">Key *</label>
                                <input type="text" class="form-control" id="edit_key" name="key" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="edit_page" class="form-label">Page *</label>
                                <select class="form-select" id="edit_page" name="page" required>
                                    <option value="homepage">Homepage</option>
                                    <option value="about">About</option>
                                    <option value="contact">Contact</option>
                                    <option value="general">General</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="edit_section" class="form-label">Section</label>
                                <input type="text" class="form-control" id="edit_section" name="section">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="edit_type" class="form-label">Type *</label>
                                <select class="form-select" id="edit_type" name="type" required>
                                    <option value="text">Text</option>
                                    <option value="textarea">Textarea</option>
                                    <option value="html">HTML</option>
                                    <option value="image">Image</option>
                                    <option value="json">JSON</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_content" class="form-label">Content</label>
                        <textarea class="form-control" id="edit_content" name="content" rows="4"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_description" class="form-label">Description</label>
                        <input type="text" class="form-control" id="edit_description" name="description">
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_sort_order" class="form-label">Sort Order</label>
                                <input type="number" class="form-control" id="edit_sort_order" name="sort_order" min="0">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="edit_is_active" name="is_active">
                                    <label class="form-check-label" for="edit_is_active">
                                        Active
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Content</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Search and filter functionality
document.getElementById('searchInput').addEventListener('input', function() {
    filterContent();
});

document.getElementById('pageFilter').addEventListener('change', function() {
    filterContent();
});

document.getElementById('sectionFilter').addEventListener('change', function() {
    filterContent();
});

function filterContent() {
    const search = document.getElementById('searchInput').value;
    const page = document.getElementById('pageFilter').value;
    const section = document.getElementById('sectionFilter').value;

    const params = new URLSearchParams();
    if (search) params.append('search', search);
    if (page) params.append('page', page);
    if (section) params.append('section', section);

    window.location.href = '{{ route("admin.content") }}?' + params.toString();
}

function clearFilters() {
    window.location.href = '{{ route("admin.content") }}';
}

// Add Content Form
document.getElementById('addContentForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Adding...';
    submitBtn.disabled = true;

    const formData = new FormData(this);

    try {
        const response = await fetch('{{ route("admin.content.store") }}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });

        const result = await response.json();

        if (response.ok) {
            bootstrap.Modal.getInstance(document.getElementById('addContentModal')).hide();
            location.reload();
        } else {
            alert(result.message || 'Error saving content');
        }
    } catch (error) {
        alert('Error saving content');
    } finally {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }
});

// Edit Content
async function editContent(id) {
    try {
        const response = await fetch(`/admin/content/${id}/edit`);
        const content = await response.json();

        // Populate edit form
        document.getElementById('edit_content_id').value = content.id;
        document.getElementById('edit_title').value = content.title;
        document.getElementById('edit_key').value = content.key;
        document.getElementById('edit_page').value = content.page;
        document.getElementById('edit_section').value = content.section || '';
        document.getElementById('edit_type').value = content.type;
        document.getElementById('edit_content').value = content.content || '';
        document.getElementById('edit_description').value = content.description || '';
        document.getElementById('edit_sort_order').value = content.sort_order;
        document.getElementById('edit_is_active').checked = content.is_active;

        // Show modal
        new bootstrap.Modal(document.getElementById('editContentModal')).show();
    } catch (error) {
        alert('Error loading content data');
    }
}

// Edit Content Form
document.getElementById('editContentForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Updating...';
    submitBtn.disabled = true;

    const formData = new FormData(this);
    const contentId = document.getElementById('edit_content_id').value;

    try {
        const response = await fetch(`/admin/content/${contentId}`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });

        const result = await response.json();

        if (response.ok) {
            bootstrap.Modal.getInstance(document.getElementById('editContentModal')).hide();
            location.reload();
        } else {
            alert(result.message || 'Error updating content');
        }
    } catch (error) {
        alert('Error updating content');
    } finally {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }
});

// Delete Content
async function deleteContent(id) {
    if (!confirm('Are you sure you want to delete this content item?')) {
        return;
    }

    try {
        const response = await fetch(`/admin/content/${id}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });

        const result = await response.json();

        if (response.ok) {
            location.reload();
        } else {
            alert(result.message || 'Error deleting content');
        }
    } catch (error) {
        alert('Error deleting content');
    }
}

// Auto-generate key from title
document.getElementById('add_title').addEventListener('input', function() {
    const title = this.value;
    const key = title.toLowerCase()
        .replace(/[^a-z0-9\s]/g, '')
        .replace(/\s+/g, '_')
        .replace(/^_+|_+$/g, '');

    if (key) {
        const page = document.getElementById('add_page').value || 'homepage';
        const section = document.getElementById('add_section').value;
        let generatedKey = page;
        if (section) generatedKey += '_' + section;
        generatedKey += '_' + key;

        document.getElementById('add_key').value = generatedKey;
    }
});

// Update key when page or section changes
document.getElementById('add_page').addEventListener('change', updateGeneratedKey);
document.getElementById('add_section').addEventListener('input', updateGeneratedKey);

function updateGeneratedKey() {
    const title = document.getElementById('add_title').value;
    if (title) {
        document.getElementById('add_title').dispatchEvent(new Event('input'));
    }
}
</script>
@endpush
