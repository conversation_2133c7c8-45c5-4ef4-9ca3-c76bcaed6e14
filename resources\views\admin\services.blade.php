@extends('layouts.admin')

@section('title', 'Services - Admin Panel')
@section('page-title', 'Services Management')

@section('content')
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="d-flex align-items-center">
            <h4 class="mb-0 me-3">Services</h4>
            <span class="badge bg-primary">{{ $services->total() }} Total</span>
        </div>
    </div>
    <div class="col-lg-4 text-end">
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addServiceModal">
            <i class="fas fa-plus me-2"></i>Add New Service
        </button>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="{{ route('admin.services') }}" class="row g-3">
            <div class="col-md-6">
                <label for="search" class="form-label">Search Services</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="{{ request('search') }}" placeholder="Search by title...">
            </div>
            <div class="col-md-3">
                <label for="status" class="form-label">Status</label>
                <select class="form-select" id="status" name="status">
                    <option value="">All Status</option>
                    <option value="1" {{ request('status') == '1' ? 'selected' : '' }}>Active</option>
                    <option value="0" {{ request('status') == '0' ? 'selected' : '' }}>Inactive</option>
                </select>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-outline-primary me-2">
                    <i class="fas fa-search"></i>
                </button>
                <a href="{{ route('admin.services') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-times"></i>
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Services Grid -->
<div class="row">
    @if($services->count() > 0)
        @foreach($services as $service)
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card h-100">
                @if($service->image)
                    <img src="{{ asset('storage/' . $service->image) }}" 
                         class="card-img-top" 
                         style="height: 200px; object-fit: cover;" 
                         alt="{{ $service->title }}">
                @else
                    <div class="card-img-top bg-light d-flex align-items-center justify-content-center" 
                         style="height: 200px;">
                        <i class="fas fa-tools fa-3x text-muted"></i>
                    </div>
                @endif
                
                <div class="card-body d-flex flex-column">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <h5 class="card-title mb-0">{{ $service->title }}</h5>
                        <div class="d-flex gap-1">
                            @if($service->is_featured)
                                <span class="badge bg-warning text-dark">Featured</span>
                            @endif
                            <span class="badge bg-{{ $service->is_active ? 'success' : 'secondary' }}">
                                {{ $service->is_active ? 'Active' : 'Inactive' }}
                            </span>
                        </div>
                    </div>
                    
                    <p class="card-text text-muted small flex-grow-1">
                        {{ Str::limit($service->short_description ?: $service->description, 100) }}
                    </p>
                    
                    <div class="mt-auto">
                        @if($service->price_from)
                            <div class="mb-2">
                                <strong class="text-primary">From ${{ number_format($service->price_from) }}</strong>
                            </div>
                        @endif
                        
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">Order: {{ $service->sort_order ?? 0 }}</small>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" 
                                        onclick="editService({{ $service->id }})"
                                        data-bs-toggle="modal" 
                                        data-bs-target="#editServiceModal">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-outline-danger" 
                                        onclick="deleteService({{ $service->id }})">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @endforeach
        
        <!-- Pagination -->
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <small class="text-muted">
                        Showing {{ $services->firstItem() }} to {{ $services->lastItem() }} 
                        of {{ $services->total() }} results
                    </small>
                </div>
                <div>
                    {{ $services->links() }}
                </div>
            </div>
        </div>
    @else
        <div class="col-12">
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="fas fa-tools fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No Services Found</h5>
                    <p class="text-muted">Start by adding your first service.</p>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addServiceModal">
                        <i class="fas fa-plus me-2"></i>Add New Service
                    </button>
                </div>
            </div>
        </div>
    @endif
</div>

<!-- Add Service Modal -->
<div class="modal fade" id="addServiceModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Service</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addServiceForm" enctype="multipart/form-data">
                @csrf
                <div class="modal-body">
                    <div class="row g-3">
                        <div class="col-md-8">
                            <label for="title" class="form-label">Service Title *</label>
                            <input type="text" class="form-control" id="title" name="title" required>
                        </div>
                        <div class="col-md-4">
                            <label for="sort_order" class="form-label">Sort Order</label>
                            <input type="number" class="form-control" id="sort_order" name="sort_order" value="0">
                        </div>
                        <div class="col-12">
                            <label for="short_description" class="form-label">Short Description</label>
                            <textarea class="form-control" id="short_description" name="short_description" rows="2"></textarea>
                        </div>
                        <div class="col-12">
                            <label for="description" class="form-label">Full Description</label>
                            <textarea class="form-control" id="description" name="description" rows="4"></textarea>
                        </div>
                        <div class="col-md-8">
                            <label for="price_from" class="form-label">Price From</label>
                            <input type="number" class="form-control" id="price_from" name="price_from" min="0" step="0.01" placeholder="Starting price">
                        </div>
                        <div class="col-md-4">
                            <label for="icon" class="form-label">Icon Class</label>
                            <input type="text" class="form-control" id="icon" name="icon" placeholder="e.g., fas fa-hammer">
                        </div>
                        <div class="col-md-6">
                            <label for="image" class="form-label">Service Image</label>
                            <input type="file" class="form-control" id="image" name="image" accept="image/*">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Options</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" checked>
                                <label class="form-check-label" for="is_active">Active</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured" value="1">
                                <label class="form-check-label" for="is_featured">Featured</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Save Service
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Service Modal -->
<div class="modal fade" id="editServiceModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Service</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editServiceForm" enctype="multipart/form-data">
                @csrf
                @method('PUT')
                <input type="hidden" id="edit_service_id" name="service_id">
                <div class="modal-body">
                    <div class="row g-3">
                        <div class="col-md-8">
                            <label for="edit_title" class="form-label">Service Title *</label>
                            <input type="text" class="form-control" id="edit_title" name="title" required>
                        </div>
                        <div class="col-md-4">
                            <label for="edit_sort_order" class="form-label">Sort Order</label>
                            <input type="number" class="form-control" id="edit_sort_order" name="sort_order" value="0">
                        </div>
                        <div class="col-12">
                            <label for="edit_short_description" class="form-label">Short Description</label>
                            <textarea class="form-control" id="edit_short_description" name="short_description" rows="2"></textarea>
                        </div>
                        <div class="col-12">
                            <label for="edit_description" class="form-label">Full Description</label>
                            <textarea class="form-control" id="edit_description" name="description" rows="4"></textarea>
                        </div>
                        <div class="col-md-8">
                            <label for="edit_price_from" class="form-label">Price From</label>
                            <input type="number" class="form-control" id="edit_price_from" name="price_from" min="0" step="0.01" placeholder="Starting price">
                        </div>
                        <div class="col-md-4">
                            <label for="edit_icon" class="form-label">Icon Class</label>
                            <input type="text" class="form-control" id="edit_icon" name="icon" placeholder="e.g., fas fa-hammer">
                        </div>
                        <div class="col-md-6">
                            <label for="edit_image" class="form-label">Update Service Image</label>
                            <input type="file" class="form-control" id="edit_image" name="image" accept="image/*">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Options</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="edit_is_active" name="is_active" value="1">
                                <label class="form-check-label" for="edit_is_active">Active</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="edit_is_featured" name="is_featured" value="1">
                                <label class="form-check-label" for="edit_is_featured">Featured</label>
                            </div>
                        </div>
                        <div class="col-12" id="current_service_image">
                            <!-- Current image will be loaded here -->
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Update Service
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Add Service Form
document.getElementById('addServiceForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;

    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Saving...';
    submitBtn.disabled = true;

    try {
        const response = await fetch('{{ route("admin.services.store") }}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });

        const result = await response.json();

        if (response.ok) {
            bootstrap.Modal.getInstance(document.getElementById('addServiceModal')).hide();
            location.reload();
        } else {
            alert(result.message || 'Error saving service');
        }
    } catch (error) {
        alert('Error saving service');
    } finally {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }
});

// Edit Service
async function editService(id) {
    try {
        const response = await fetch(`/admin/services/${id}/edit`);
        const service = await response.json();

        document.getElementById('edit_service_id').value = service.id;
        document.getElementById('edit_title').value = service.title;
        document.getElementById('edit_sort_order').value = service.sort_order || 0;
        document.getElementById('edit_short_description').value = service.short_description || '';
        document.getElementById('edit_description').value = service.description || '';
        document.getElementById('edit_price_from').value = service.price_from || '';
        document.getElementById('edit_icon').value = service.icon || '';
        document.getElementById('edit_is_active').checked = service.is_active;
        document.getElementById('edit_is_featured').checked = service.is_featured;

        // Load current image
        const currentImageDiv = document.getElementById('current_service_image');
        if (service.image) {
            currentImageDiv.innerHTML = `
                <label class="form-label">Current Image</label>
                <div class="position-relative d-inline-block">
                    <img src="/storage/${service.image}" class="rounded" style="width: 150px; height: 100px; object-fit: cover;">
                    <button type="button" class="btn btn-sm btn-danger position-absolute top-0 end-0"
                            onclick="removeServiceImage(${service.id})" style="transform: translate(50%, -50%);">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
        } else {
            currentImageDiv.innerHTML = '';
        }
    } catch (error) {
        alert('Error loading service data');
    }
}

// Update Service Form
document.getElementById('editServiceForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const serviceId = document.getElementById('edit_service_id').value;
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;

    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Updating...';
    submitBtn.disabled = true;

    try {
        const response = await fetch(`/admin/services/${serviceId}`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });

        const result = await response.json();

        if (response.ok) {
            bootstrap.Modal.getInstance(document.getElementById('editServiceModal')).hide();
            location.reload();
        } else {
            alert(result.message || 'Error updating service');
        }
    } catch (error) {
        alert('Error updating service');
    } finally {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }
});

// Delete Service
async function deleteService(id) {
    if (!confirm('Are you sure you want to delete this service? This action cannot be undone.')) {
        return;
    }

    try {
        const response = await fetch(`/admin/services/${id}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();

        if (response.ok) {
            location.reload();
        } else {
            alert(result.message || 'Error deleting service');
        }
    } catch (error) {
        alert('Error deleting service');
    }
}

// Remove Service Image
async function removeServiceImage(serviceId) {
    if (!confirm('Are you sure you want to remove this image?')) {
        return;
    }

    try {
        const response = await fetch(`/admin/services/${serviceId}/remove-image`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();

        if (response.ok) {
            editService(serviceId); // Reload the edit form
        } else {
            alert(result.message || 'Error removing image');
        }
    } catch (error) {
        alert('Error removing image');
    }
}
</script>
@endpush
