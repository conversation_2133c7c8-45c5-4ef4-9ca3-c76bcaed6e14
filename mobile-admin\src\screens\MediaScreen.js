import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Image,
  Alert,
  RefreshControl,
  Modal,
  TextInput,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import { apiService } from '../services/apiService';

const { width } = Dimensions.get('window');
const itemSize = (width - 60) / 2; // 2 columns with padding

const MediaScreen = () => {
  const [media, setMedia] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [selectedMedia, setSelectedMedia] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [uploadModalVisible, setUploadModalVisible] = useState(false);
  const [altText, setAltText] = useState('');
  const [description, setDescription] = useState('');

  useEffect(() => {
    loadMedia();
    requestPermissions();
    testServerConnection();
  }, []);

  const testServerConnection = async () => {
    try {
      console.log('Testing server connection...');
      const response = await fetch('http://***********:8000/api/media', {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
      });
      console.log('Server connection test - Status:', response.status);
      console.log('Server connection test - OK:', response.ok);
    } catch (error) {
      console.error('Server connection test failed:', error);
      Alert.alert(
        'Connection Issue',
        'Cannot connect to server. Please check:\n1. Laravel server is running\n2. IP address is correct\n3. Network connectivity'
      );
    }
  };

  // Alternative upload method using fetch directly
  const uploadMediaAlternative = async () => {
    if (!selectedMedia) return;

    setUploading(true);
    try {
      console.log('Alternative upload method - Starting upload for:', selectedMedia);

      const formData = new FormData();

      // Use fetch API directly with proper file handling
      const response = await fetch(selectedMedia.uri);
      const blob = await response.blob();

      formData.append('files[]', blob, selectedMedia.fileName || `image_${Date.now()}.jpg`);
      if (altText) formData.append('alt_text', altText);
      if (description) formData.append('description', description);

      const uploadResponse = await fetch('http://***********:8000/api/media', {
        method: 'POST',
        body: formData,
        headers: {
          'Accept': 'application/json',
          // Don't set Content-Type, let the browser set it with boundary
        },
      });

      if (uploadResponse.ok) {
        const result = await uploadResponse.json();
        console.log('Alternative upload successful:', result);
        Alert.alert('Success', 'Media uploaded successfully');
        setUploadModalVisible(false);
        setSelectedMedia(null);
        setAltText('');
        setDescription('');
        loadMedia();
      } else {
        const errorData = await uploadResponse.text();
        console.error('Alternative upload failed:', errorData);
        Alert.alert('Error', 'Upload failed: ' + uploadResponse.status);
      }
    } catch (error) {
      console.error('Alternative upload error:', error);
      Alert.alert('Error', 'Upload failed: ' + error.message);
    } finally {
      setUploading(false);
    }
  };

  const requestPermissions = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert('Permission needed', 'Sorry, we need camera roll permissions to upload images.');
    }
  };

  const loadMedia = async () => {
    try {
      const response = await apiService.getMedia();
      setMedia(response.data || []);
    } catch (error) {
      console.error('Error loading media:', error);
      Alert.alert('Error', 'Failed to load media files');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadMedia();
  };

  const pickImage = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
        allowsMultipleSelection: false,
      });

      if (!result.canceled && result.assets[0]) {
        setUploadModalVisible(true);
        setSelectedMedia(result.assets[0]);
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Error', 'Failed to pick image');
    }
  };

  const uploadMedia = async () => {
    if (!selectedMedia) return;

    setUploading(true);
    try {
      console.log('Starting upload for:', selectedMedia);

      const formData = new FormData();

      // React Native FormData requires specific format for file uploads
      const fileUri = selectedMedia.uri;
      const fileType = selectedMedia.type || selectedMedia.mimeType || 'image/jpeg';
      const fileName = selectedMedia.fileName || selectedMedia.name || `image_${Date.now()}.jpg`;

      formData.append('files[]', {
        uri: fileUri,
        type: fileType,
        name: fileName,
      });

      if (altText) formData.append('alt_text', altText);
      if (description) formData.append('description', description);

      console.log('FormData prepared, uploading...');
      const response = await apiService.uploadMedia(formData);
      console.log('Upload response:', response);

      Alert.alert('Success', 'Media uploaded successfully');
      setUploadModalVisible(false);
      setSelectedMedia(null);
      setAltText('');
      setDescription('');
      loadMedia();
    } catch (error) {
      console.error('Error uploading media:', error);
      console.error('Error details:', {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status,
        config: error.config
      });

      let errorMessage = 'Failed to upload media';
      if (error.message === 'Network Error') {
        errorMessage = 'Network Error: Please check if the server is running and accessible';
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }

      Alert.alert('Error', errorMessage);
    } finally {
      setUploading(false);
    }
  };

  const deleteMedia = async (mediaId) => {
    Alert.alert(
      'Delete Media',
      'Are you sure you want to delete this media file?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await apiService.deleteMedia(mediaId);
              Alert.alert('Success', 'Media deleted successfully');
              loadMedia();
            } catch (error) {
              console.error('Error deleting media:', error);
              Alert.alert('Error', 'Failed to delete media');
            }
          },
        },
      ]
    );
  };

  const viewMedia = (item) => {
    setSelectedMedia(item);
    setModalVisible(true);
  };

  const renderMediaItem = ({ item }) => {
    const isImage = item.file_type?.startsWith('image/') || item.mime_type?.startsWith('image/');
    const baseUrl = 'http://***********:8000/storage/'; // Update with your server URL

    return (
      <TouchableOpacity
        style={styles.mediaItem}
        onPress={() => viewMedia(item)}
      >
        {isImage ? (
          <Image
            source={{ uri: baseUrl + item.file_path }}
            style={styles.mediaImage}
            resizeMode="cover"
          />
        ) : (
          <View style={styles.mediaPlaceholder}>
            <Ionicons
              name={item.file_type?.startsWith('video/') ? 'videocam' : 'document'}
              size={40}
              color="#666"
            />
          </View>
        )}

        <View style={styles.mediaOverlay}>
          <TouchableOpacity
            style={styles.deleteButton}
            onPress={() => deleteMedia(item.id)}
          >
            <Ionicons name="trash" size={16} color="#fff" />
          </TouchableOpacity>
        </View>

        <View style={styles.mediaInfo}>
          <Text style={styles.mediaTitle} numberOfLines={1}>
            {item.title || item.original_name || 'Untitled'}
          </Text>
          <Text style={styles.mediaSize}>
            {item.file_size ? `${(item.file_size / 1024).toFixed(1)} KB` : 'Unknown size'}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#FFB703" />
        <Text style={styles.loadingText}>Loading media...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Media Library</Text>
        <TouchableOpacity style={styles.uploadButton} onPress={pickImage}>
          <Ionicons name="add" size={24} color="#fff" />
        </TouchableOpacity>
      </View>

      {/* Media Grid */}
      {media.length > 0 ? (
        <FlatList
          data={media}
          renderItem={renderMediaItem}
          keyExtractor={(item) => item.id.toString()}
          numColumns={2}
          contentContainerStyle={styles.mediaGrid}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
        />
      ) : (
        <View style={styles.emptyContainer}>
          <Ionicons name="images-outline" size={80} color="#ccc" />
          <Text style={styles.emptyTitle}>No Media Files</Text>
          <Text style={styles.emptyDescription}>
            Start by uploading your first media file
          </Text>
          <TouchableOpacity style={styles.emptyButton} onPress={pickImage}>
            <Ionicons name="cloud-upload-outline" size={20} color="#fff" />
            <Text style={styles.emptyButtonText}>Upload Media</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Upload Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={uploadModalVisible}
        onRequestClose={() => setUploadModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Upload Media</Text>
              <TouchableOpacity onPress={() => setUploadModalVisible(false)}>
                <Ionicons name="close" size={24} color="#666" />
              </TouchableOpacity>
            </View>

            {selectedMedia && (
              <Image
                source={{ uri: selectedMedia.uri }}
                style={styles.previewImage}
                resizeMode="cover"
              />
            )}

            <TextInput
              style={styles.input}
              placeholder="Alt text (for accessibility)"
              value={altText}
              onChangeText={setAltText}
            />

            <TextInput
              style={[styles.input, styles.textArea]}
              placeholder="Description (optional)"
              value={description}
              onChangeText={setDescription}
              multiline
              numberOfLines={3}
            />

            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => setUploadModalVisible(false)}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.modalButton, styles.uploadButtonModal]}
                onPress={uploadMedia}
                disabled={uploading}
              >
                {uploading ? (
                  <ActivityIndicator size="small" color="#fff" />
                ) : (
                  <Text style={styles.uploadButtonText}>Upload</Text>
                )}
              </TouchableOpacity>
            </View>

            {/* Alternative upload button for testing */}
            <TouchableOpacity
              style={[styles.modalButton, styles.alternativeButton]}
              onPress={uploadMediaAlternative}
              disabled={uploading}
            >
              <Text style={styles.alternativeButtonText}>
                Try Alternative Upload
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* View Media Modal */}
      <Modal
        animationType="fade"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.fullScreenModalOverlay}>
          <TouchableOpacity
            style={styles.closeFullScreen}
            onPress={() => setModalVisible(false)}
          >
            <Ionicons name="close" size={30} color="#fff" />
          </TouchableOpacity>

          {selectedMedia && (
            <View style={styles.fullScreenContent}>
              {selectedMedia.file_type?.startsWith('image/') || selectedMedia.mime_type?.startsWith('image/') ? (
                <Image
                  source={{ uri: `http://***********:8000/storage/${selectedMedia.file_path}` }}
                  style={styles.fullScreenImage}
                  resizeMode="contain"
                />
              ) : (
                <View style={styles.fullScreenPlaceholder}>
                  <Ionicons
                    name={selectedMedia.file_type?.startsWith('video/') ? 'videocam' : 'document'}
                    size={80}
                    color="#fff"
                  />
                  <Text style={styles.fullScreenText}>
                    {selectedMedia.title || selectedMedia.original_name}
                  </Text>
                </View>
              )}
            </View>
          )}
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#023047',
  },
  uploadButton: {
    backgroundColor: '#FFB703',
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  mediaGrid: {
    padding: 20,
  },
  mediaItem: {
    width: itemSize,
    marginRight: 20,
    marginBottom: 20,
    backgroundColor: '#fff',
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  mediaImage: {
    width: '100%',
    height: itemSize * 0.7,
  },
  mediaPlaceholder: {
    width: '100%',
    height: itemSize * 0.7,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  mediaOverlay: {
    position: 'absolute',
    top: 8,
    right: 8,
  },
  deleteButton: {
    backgroundColor: 'rgba(255, 0, 0, 0.8)',
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
  },
  mediaInfo: {
    padding: 12,
  },
  mediaTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#023047',
    marginBottom: 4,
  },
  mediaSize: {
    fontSize: 12,
    color: '#666',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#023047',
    marginTop: 20,
    marginBottom: 10,
  },
  emptyDescription: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 30,
    lineHeight: 24,
  },
  emptyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFB703',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 25,
  },
  emptyButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    marginLeft: 8,
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    width: '90%',
    maxWidth: 400,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#023047',
  },
  previewImage: {
    width: '100%',
    height: 200,
    borderRadius: 8,
    marginBottom: 20,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    marginBottom: 15,
    fontSize: 16,
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 10,
  },
  modalButton: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: '#f0f0f0',
    marginRight: 10,
  },
  cancelButtonText: {
    color: '#666',
    fontWeight: '600',
  },
  uploadButtonModal: {
    backgroundColor: '#FFB703',
    marginLeft: 10,
  },
  uploadButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  alternativeButton: {
    backgroundColor: '#6c757d',
    marginTop: 10,
  },
  alternativeButtonText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 14,
  },
  // Full screen modal styles
  fullScreenModalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeFullScreen: {
    position: 'absolute',
    top: 50,
    right: 20,
    zIndex: 1,
    padding: 10,
  },
  fullScreenContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
  },
  fullScreenImage: {
    width: '100%',
    height: '100%',
  },
  fullScreenPlaceholder: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  fullScreenText: {
    color: '#fff',
    fontSize: 18,
    marginTop: 20,
    textAlign: 'center',
  },
});

export default MediaScreen;
