import React from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  ActivityIndicator,
  View,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import theme from '../../theme';

const Button = ({
  title,
  onPress,
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  icon,
  iconPosition = 'left',
  gradient = false,
  fullWidth = false,
  style,
  textStyle,
  ...props
}) => {
  const getButtonStyle = () => {
    const baseStyle = [styles.button];
    
    // Size styles
    switch (size) {
      case 'sm':
        baseStyle.push(styles.buttonSm);
        break;
      case 'lg':
        baseStyle.push(styles.buttonLg);
        break;
      case 'xl':
        baseStyle.push(styles.buttonXl);
        break;
      default:
        baseStyle.push(styles.buttonMd);
    }
    
    // Variant styles
    switch (variant) {
      case 'secondary':
        baseStyle.push(styles.buttonSecondary);
        break;
      case 'outline':
        baseStyle.push(styles.buttonOutline);
        break;
      case 'ghost':
        baseStyle.push(styles.buttonGhost);
        break;
      case 'danger':
        baseStyle.push(styles.buttonDanger);
        break;
      case 'success':
        baseStyle.push(styles.buttonSuccess);
        break;
      default:
        baseStyle.push(styles.buttonPrimary);
    }
    
    // State styles
    if (disabled) {
      baseStyle.push(styles.buttonDisabled);
    }
    
    if (fullWidth) {
      baseStyle.push(styles.buttonFullWidth);
    }
    
    return baseStyle;
  };
  
  const getTextStyle = () => {
    const baseStyle = [styles.buttonText];
    
    // Size text styles
    switch (size) {
      case 'sm':
        baseStyle.push(styles.buttonTextSm);
        break;
      case 'lg':
        baseStyle.push(styles.buttonTextLg);
        break;
      case 'xl':
        baseStyle.push(styles.buttonTextXl);
        break;
      default:
        baseStyle.push(styles.buttonTextMd);
    }
    
    // Variant text styles
    switch (variant) {
      case 'outline':
        baseStyle.push(styles.buttonTextOutline);
        break;
      case 'ghost':
        baseStyle.push(styles.buttonTextGhost);
        break;
      default:
        baseStyle.push(styles.buttonTextSolid);
    }
    
    if (disabled) {
      baseStyle.push(styles.buttonTextDisabled);
    }
    
    return baseStyle;
  };
  
  const getIconSize = () => {
    switch (size) {
      case 'sm': return 16;
      case 'lg': return 24;
      case 'xl': return 28;
      default: return 20;
    }
  };
  
  const getGradientColors = () => {
    switch (variant) {
      case 'secondary':
        return theme.colors.gradients.secondary;
      case 'danger':
        return ['#F44336', '#D32F2F'];
      case 'success':
        return ['#4CAF50', '#388E3C'];
      default:
        return theme.colors.gradients.primary;
    }
  };
  
  const renderContent = () => {
    const iconColor = variant === 'outline' || variant === 'ghost' 
      ? theme.colors.primary.main 
      : theme.colors.text.inverse;
    
    return (
      <View style={styles.buttonContent}>
        {loading ? (
          <ActivityIndicator 
            size="small" 
            color={iconColor}
            style={styles.loadingIndicator}
          />
        ) : (
          <>
            {icon && iconPosition === 'left' && (
              <Ionicons 
                name={icon} 
                size={getIconSize()} 
                color={iconColor}
                style={styles.iconLeft}
              />
            )}
            <Text style={[getTextStyle(), textStyle]}>{title}</Text>
            {icon && iconPosition === 'right' && (
              <Ionicons 
                name={icon} 
                size={getIconSize()} 
                color={iconColor}
                style={styles.iconRight}
              />
            )}
          </>
        )}
      </View>
    );
  };
  
  if (gradient && variant !== 'outline' && variant !== 'ghost') {
    return (
      <TouchableOpacity
        onPress={onPress}
        disabled={disabled || loading}
        style={[getButtonStyle(), style]}
        activeOpacity={0.8}
        {...props}
      >
        <LinearGradient
          colors={getGradientColors()}
          style={styles.gradientContainer}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
        >
          {renderContent()}
        </LinearGradient>
      </TouchableOpacity>
    );
  }
  
  return (
    <TouchableOpacity
      onPress={onPress}
      disabled={disabled || loading}
      style={[getButtonStyle(), style]}
      activeOpacity={0.8}
      {...props}
    >
      {renderContent()}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    borderRadius: theme.borderRadius.md,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    ...theme.shadows.sm,
  },
  
  // Size styles
  buttonSm: {
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    minHeight: 36,
  },
  buttonMd: {
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
    minHeight: 44,
  },
  buttonLg: {
    paddingHorizontal: theme.spacing.xl,
    paddingVertical: theme.spacing.lg,
    minHeight: 52,
  },
  buttonXl: {
    paddingHorizontal: theme.spacing['2xl'],
    paddingVertical: theme.spacing.xl,
    minHeight: 60,
  },
  
  // Variant styles
  buttonPrimary: {
    backgroundColor: theme.colors.primary.main,
    borderColor: theme.colors.primary.main,
  },
  buttonSecondary: {
    backgroundColor: theme.colors.secondary.main,
    borderColor: theme.colors.secondary.main,
  },
  buttonOutline: {
    backgroundColor: 'transparent',
    borderColor: theme.colors.primary.main,
  },
  buttonGhost: {
    backgroundColor: 'transparent',
    borderColor: 'transparent',
    shadowOpacity: 0,
    elevation: 0,
  },
  buttonDanger: {
    backgroundColor: theme.colors.accent.error,
    borderColor: theme.colors.accent.error,
  },
  buttonSuccess: {
    backgroundColor: theme.colors.accent.success,
    borderColor: theme.colors.accent.success,
  },
  
  // State styles
  buttonDisabled: {
    opacity: 0.5,
  },
  buttonFullWidth: {
    width: '100%',
  },
  
  // Text styles
  buttonText: {
    fontFamily: theme.typography.fonts.medium,
    fontWeight: theme.typography.weights.semibold,
    textAlign: 'center',
  },
  buttonTextSm: {
    fontSize: theme.typography.sizes.sm,
  },
  buttonTextMd: {
    fontSize: theme.typography.sizes.md,
  },
  buttonTextLg: {
    fontSize: theme.typography.sizes.lg,
  },
  buttonTextXl: {
    fontSize: theme.typography.sizes.xl,
  },
  buttonTextSolid: {
    color: theme.colors.text.inverse,
  },
  buttonTextOutline: {
    color: theme.colors.primary.main,
  },
  buttonTextGhost: {
    color: theme.colors.primary.main,
  },
  buttonTextDisabled: {
    opacity: 0.7,
  },
  
  // Content styles
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconLeft: {
    marginRight: theme.spacing.sm,
  },
  iconRight: {
    marginLeft: theme.spacing.sm,
  },
  loadingIndicator: {
    marginRight: theme.spacing.sm,
  },
  gradientContainer: {
    flex: 1,
    borderRadius: theme.borderRadius.md - 2,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default Button;
