<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login - ConstructCo</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #FFB703;
            --secondary-color: #023047;
            --accent-color: #8ECAE6;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, var(--secondary-color) 0%, #034663 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            overflow: hidden;
            max-width: 900px;
            width: 100%;
            margin: 2rem;
        }

        .login-left {
            background: linear-gradient(135deg, var(--primary-color) 0%, #e6a503 100%);
            color: var(--secondary-color);
            padding: 3rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
            text-align: center;
        }

        .login-right {
            padding: 3rem;
        }

        .brand-logo {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .form-control {
            border: 2px solid #eee;
            border-radius: 10px;
            padding: 0.75rem 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(255, 183, 3, 0.25);
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: var(--secondary-color);
            font-weight: 600;
            padding: 0.75rem 2rem;
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background-color: #e6a503;
            border-color: #e6a503;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 183, 3, 0.3);
        }

        .form-check-input:checked {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .alert {
            border-radius: 10px;
            border: none;
        }

        @media (max-width: 768px) {
            .login-left {
                padding: 2rem;
            }
            
            .login-right {
                padding: 2rem;
            }
            
            .brand-logo {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="row g-0">
            <!-- Left Side - Branding -->
            <div class="col-lg-5 login-left">
                <div>
                    <div class="brand-logo">
                        <i class="fas fa-hard-hat mb-3"></i>
                        <div>ConstructCo</div>
                    </div>
                    <h3 class="mb-3">Admin Panel</h3>
                    <p class="lead mb-4">Manage your construction business with our comprehensive admin dashboard.</p>
                    
                    <div class="features">
                        <div class="feature-item mb-3">
                            <i class="fas fa-building me-2"></i>
                            <span>Project Management</span>
                        </div>
                        <div class="feature-item mb-3">
                            <i class="fas fa-tools me-2"></i>
                            <span>Service Management</span>
                        </div>
                        <div class="feature-item mb-3">
                            <i class="fas fa-images me-2"></i>
                            <span>Media Gallery</span>
                        </div>
                        <div class="feature-item mb-3">
                            <i class="fas fa-envelope me-2"></i>
                            <span>Message Center</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Right Side - Login Form -->
            <div class="col-lg-7 login-right">
                <div class="text-center mb-4">
                    <h2 class="fw-bold text-dark">Welcome Back!</h2>
                    <p class="text-muted">Please sign in to your admin account</p>
                </div>

                @if($errors->any())
                    <div class="alert alert-danger" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        @foreach($errors->all() as $error)
                            {{ $error }}
                        @endforeach
                    </div>
                @endif

                <form method="POST" action="{{ route('admin.login.submit') }}">
                    @csrf
                    
                    <div class="mb-3">
                        <label for="email" class="form-label fw-semibold">Email Address</label>
                        <div class="input-group">
                            <span class="input-group-text bg-light border-end-0" style="border: 2px solid #eee; border-right: none; border-radius: 10px 0 0 10px;">
                                <i class="fas fa-envelope text-muted"></i>
                            </span>
                            <input type="email" 
                                   class="form-control border-start-0 @error('email') is-invalid @enderror" 
                                   id="email" 
                                   name="email" 
                                   value="{{ old('email') }}" 
                                   placeholder="Enter your email"
                                   style="border-radius: 0 10px 10px 0;"
                                   required>
                        </div>
                        @error('email')
                            <div class="text-danger small mt-1">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="mb-3">
                        <label for="password" class="form-label fw-semibold">Password</label>
                        <div class="input-group">
                            <span class="input-group-text bg-light border-end-0" style="border: 2px solid #eee; border-right: none; border-radius: 10px 0 0 10px;">
                                <i class="fas fa-lock text-muted"></i>
                            </span>
                            <input type="password" 
                                   class="form-control border-start-0 @error('password') is-invalid @enderror" 
                                   id="password" 
                                   name="password" 
                                   placeholder="Enter your password"
                                   style="border-radius: 0 10px 10px 0;"
                                   required>
                        </div>
                        @error('password')
                            <div class="text-danger small mt-1">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="mb-4">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="remember" name="remember">
                            <label class="form-check-label" for="remember">
                                Remember me
                            </label>
                        </div>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-sign-in-alt me-2"></i>Sign In
                        </button>
                    </div>
                </form>
                
                <div class="text-center mt-4">
                    <small class="text-muted">
                        <i class="fas fa-shield-alt me-1"></i>
                        Secure admin access only
                    </small>
                </div>
                
                <div class="text-center mt-3">
                    <a href="{{ route('home') }}" class="text-decoration-none">
                        <i class="fas fa-arrow-left me-1"></i>Back to Website
                    </a>
                </div>
                
                <!-- Demo Credentials -->
                <div class="mt-4 p-3 bg-light rounded">
                    <small class="text-muted">
                        <strong>Demo Credentials:</strong><br>
                        Email: <EMAIL><br>
                        Password: password123
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
