<!-- Admin Sidebar -->
<div class="admin-sidebar" id="adminSidebar">
    <div class="sidebar-header">
        <div class="d-flex align-items-center">
            <div class="sidebar-logo">
                <i class="fas fa-hard-hat text-primary"></i>
            </div>
            <div class="sidebar-brand ms-2">
                <h5 class="mb-0 text-white">ConstructCo</h5>
                <small class="text-muted">Admin Panel</small>
            </div>
        </div>
        <button class="btn btn-link text-white d-lg-none" id="sidebarToggle">
            <i class="fas fa-times"></i>
        </button>
    </div>

    <div class="sidebar-content">
        <!-- User Profile Section -->
        <div class="user-profile">
            <div class="d-flex align-items-center">
                <?php if(auth()->user()->avatar): ?>
                    <img src="<?php echo e(asset('storage/' . auth()->user()->avatar)); ?>" 
                         alt="<?php echo e(auth()->user()->name); ?>" 
                         class="user-avatar">
                <?php else: ?>
                    <div class="user-avatar-placeholder">
                        <i class="fas fa-user"></i>
                    </div>
                <?php endif; ?>
                <div class="user-info ms-2">
                    <div class="user-name"><?php echo e(auth()->user()->name); ?></div>
                    <div class="user-role"><?php echo e(ucfirst(auth()->user()->role ?? 'Admin')); ?></div>
                </div>
            </div>
        </div>

        <!-- Navigation Menu -->
        <nav class="sidebar-nav">
            <ul class="nav flex-column">
                <!-- Dashboard -->
                <li class="nav-item">
                    <a href="<?php echo e(route('admin.dashboard')); ?>" 
                       class="nav-link <?php echo e(request()->routeIs('admin.dashboard') ? 'active' : ''); ?>">
                        <i class="fas fa-tachometer-alt nav-icon"></i>
                        <span class="nav-text">Dashboard</span>
                    </a>
                </li>

                <!-- Projects -->
                <li class="nav-item">
                    <a href="<?php echo e(route('admin.projects')); ?>" 
                       class="nav-link <?php echo e(request()->routeIs('admin.projects*') ? 'active' : ''); ?>">
                        <i class="fas fa-building nav-icon"></i>
                        <span class="nav-text">Projects</span>
                        <?php
                            $projectsCount = \App\Models\Project::count();
                        ?>
                        <?php if($projectsCount > 0): ?>
                            <span class="nav-badge"><?php echo e($projectsCount); ?></span>
                        <?php endif; ?>
                    </a>
                </li>

                <!-- Services -->
                <li class="nav-item">
                    <a href="<?php echo e(route('admin.services')); ?>" 
                       class="nav-link <?php echo e(request()->routeIs('admin.services*') ? 'active' : ''); ?>">
                        <i class="fas fa-tools nav-icon"></i>
                        <span class="nav-text">Services</span>
                        <?php
                            $servicesCount = \App\Models\Service::count();
                        ?>
                        <?php if($servicesCount > 0): ?>
                            <span class="nav-badge"><?php echo e($servicesCount); ?></span>
                        <?php endif; ?>
                    </a>
                </li>

                <!-- Media -->
                <li class="nav-item">
                    <a href="<?php echo e(route('admin.media')); ?>" 
                       class="nav-link <?php echo e(request()->routeIs('admin.media*') ? 'active' : ''); ?>">
                        <i class="fas fa-images nav-icon"></i>
                        <span class="nav-text">Media</span>
                        <?php
                            $mediaCount = \App\Models\Media::count();
                        ?>
                        <?php if($mediaCount > 0): ?>
                            <span class="nav-badge"><?php echo e($mediaCount); ?></span>
                        <?php endif; ?>
                    </a>
                </li>

                <!-- Messages -->
                <li class="nav-item">
                    <a href="<?php echo e(route('admin.messages')); ?>" 
                       class="nav-link <?php echo e(request()->routeIs('admin.messages*') ? 'active' : ''); ?>">
                        <i class="fas fa-envelope nav-icon"></i>
                        <span class="nav-text">Messages</span>
                        <?php
                            $unreadCount = \App\Models\Message::where('is_read', false)->count();
                        ?>
                        <?php if($unreadCount > 0): ?>
                            <span class="nav-badge bg-danger"><?php echo e($unreadCount); ?></span>
                        <?php endif; ?>
                    </a>
                </li>

                <!-- Content -->
                <li class="nav-item">
                    <a href="<?php echo e(route('admin.content')); ?>" 
                       class="nav-link <?php echo e(request()->routeIs('admin.content*') ? 'active' : ''); ?>">
                        <i class="fas fa-file-alt nav-icon"></i>
                        <span class="nav-text">Content</span>
                    </a>
                </li>

                <!-- Divider -->
                <li class="nav-divider"></li>

                <!-- Settings -->
                <li class="nav-item">
                    <a href="<?php echo e(route('admin.settings')); ?>" 
                       class="nav-link <?php echo e(request()->routeIs('admin.settings*') ? 'active' : ''); ?>">
                        <i class="fas fa-cog nav-icon"></i>
                        <span class="nav-text">Settings</span>
                    </a>
                </li>

                <!-- Profile -->
                <li class="nav-item">
                    <a href="<?php echo e(route('admin.profile')); ?>" 
                       class="nav-link <?php echo e(request()->routeIs('admin.profile*') ? 'active' : ''); ?>">
                        <i class="fas fa-user-circle nav-icon"></i>
                        <span class="nav-text">Profile</span>
                    </a>
                </li>

                <!-- Divider -->
                <li class="nav-divider"></li>

                <!-- Frontend Links -->
                <li class="nav-item">
                    <a href="<?php echo e(route('home')); ?>" 
                       class="nav-link" 
                       target="_blank">
                        <i class="fas fa-external-link-alt nav-icon"></i>
                        <span class="nav-text">View Website</span>
                    </a>
                </li>

                <!-- Logout -->
                <li class="nav-item">
                    <form action="<?php echo e(route('admin.logout')); ?>" method="POST" class="d-inline">
                        <?php echo csrf_field(); ?>
                        <button type="submit" class="nav-link btn btn-link text-start w-100 border-0 p-0">
                            <i class="fas fa-sign-out-alt nav-icon"></i>
                            <span class="nav-text">Logout</span>
                        </button>
                    </form>
                </li>
            </ul>
        </nav>
    </div>

    <!-- Sidebar Footer -->
    <div class="sidebar-footer">
        <div class="text-center">
            <small class="text-muted">
                © <?php echo e(date('Y')); ?> ConstructCo<br>
                Admin Panel v1.0
            </small>
        </div>
    </div>
</div>

<!-- Sidebar Overlay for Mobile -->
<div class="sidebar-overlay d-lg-none" id="sidebarOverlay"></div>

<style>
.admin-sidebar {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    width: 280px;
    background: linear-gradient(180deg, #023047 0%, #034663 100%);
    color: white;
    z-index: 1050;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

.admin-sidebar.show {
    transform: translateX(0);
}

@media (min-width: 992px) {
    .admin-sidebar {
        position: relative;
        transform: translateX(0);
    }
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.sidebar-logo {
    font-size: 1.5rem;
}

.sidebar-content {
    flex: 1;
    padding: 1rem 0;
}

.user-profile {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: 1rem;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.user-avatar-placeholder {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
}

.user-name {
    font-weight: 600;
    font-size: 0.9rem;
}

.user-role {
    font-size: 0.8rem;
    color: #FFB703;
}

.sidebar-nav .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 0.75rem 1.5rem;
    border-radius: 0;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    text-decoration: none;
    border: none;
    background: none;
}

.sidebar-nav .nav-link:hover {
    color: white;
    background: rgba(255, 255, 255, 0.1);
}

.sidebar-nav .nav-link.active {
    color: white;
    background: rgba(255, 183, 3, 0.2);
    border-right: 3px solid #FFB703;
}

.nav-icon {
    width: 20px;
    margin-right: 0.75rem;
    text-align: center;
}

.nav-text {
    flex: 1;
}

.nav-badge {
    background: #FFB703;
    color: #023047;
    font-size: 0.7rem;
    padding: 0.2rem 0.5rem;
    border-radius: 10px;
    font-weight: 600;
}

.nav-badge.bg-danger {
    background: #dc3545 !important;
    color: white !important;
}

.nav-divider {
    height: 1px;
    background: rgba(255, 255, 255, 0.1);
    margin: 1rem 1.5rem;
}

.sidebar-footer {
    padding: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1040;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.sidebar-overlay.show {
    opacity: 1;
    visibility: visible;
}
</style>
<?php /**PATH C:\xampp\htdocs\new-project-app\resources\views/admin/partials/sidebar.blade.php ENDPATH**/ ?>