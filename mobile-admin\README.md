# ConstructCo Mobile Admin App

A React Native mobile application for managing the ConstructCo construction website. This app allows administrators to manage projects, services, media, and customer messages on the go.

## Features

### 🏗️ **Project Management**
- View all construction projects
- Add new projects with detailed information
- Edit existing projects
- Delete projects
- Filter projects by status and category
- Upload project images

### 🔧 **Service Management**
- Manage service offerings
- Add new services with pricing
- Edit service details
- Toggle featured services
- Set service availability

### 📱 **Dashboard**
- Overview of key statistics
- Quick access to main functions
- Recent activity monitoring
- Real-time data updates

### 📧 **Message Center**
- View customer inquiries
- Manage contact form submissions
- Update message status
- Respond to customer messages

### 📸 **Media Management**
- Upload photos and videos
- Organize media files
- Delete unused media
- Manage project galleries

### 👤 **Profile & Settings**
- User profile management
- App settings and preferences
- Secure logout functionality

## Technology Stack

- **Framework**: React Native with Expo
- **Navigation**: React Navigation 6
- **State Management**: React Context API
- **HTTP Client**: Axios
- **Secure Storage**: Expo SecureStore
- **Icons**: Expo Vector Icons
- **Authentication**: Token-based with Laravel Sanctum

## Prerequisites

Before running the mobile app, ensure you have:

1. **Node.js** (v16 or higher)
2. **npm** or **yarn**
3. **Expo CLI** (`npm install -g @expo/cli`)
4. **Laravel Backend** running (see main project README)
5. **Mobile device** or **emulator** for testing

## Installation

1. **Navigate to mobile app directory:**
   ```bash
   cd mobile-admin
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Configure API endpoint:**
   Edit `src/services/apiService.js` and update the `BASE_URL`:
   ```javascript
   const BASE_URL = 'http://YOUR_IP_ADDRESS:8000/api';
   ```
   Replace `YOUR_IP_ADDRESS` with your computer's IP address.

## Running the App

### Development Mode

1. **Start the Expo development server:**
   ```bash
   npm start
   ```

2. **Run on specific platform:**
   ```bash
   # Android
   npm run android
   
   # iOS (macOS only)
   npm run ios
   
   # Web
   npm run web
   ```

### Using Expo Go App

1. Install **Expo Go** on your mobile device
2. Scan the QR code from the terminal
3. The app will load on your device

## Configuration

### API Configuration

Update the API base URL in `src/services/apiService.js`:

```javascript
// For local development
const BASE_URL = 'http://*************:8000/api';

// For production
const BASE_URL = 'https://your-domain.com/api';
```

### Authentication

The app uses token-based authentication with Laravel Sanctum:

- Tokens are stored securely using Expo SecureStore
- Automatic token refresh on app startup
- Secure logout with token cleanup

## Default Login Credentials

```
Email: <EMAIL>
Password: password123
```

## Project Structure

```
mobile-admin/
├── src/
│   ├── context/
│   │   └── AuthContext.js          # Authentication context
│   ├── screens/
│   │   ├── LoginScreen.js          # Login interface
│   │   ├── DashboardScreen.js      # Main dashboard
│   │   ├── ProjectsScreen.js       # Projects management
│   │   ├── AddProjectScreen.js     # Add new project
│   │   ├── ServicesScreen.js       # Services management
│   │   ├── AddServiceScreen.js     # Add new service
│   │   ├── MediaScreen.js          # Media management
│   │   ├── MessagesScreen.js       # Customer messages
│   │   └── ProfileScreen.js        # User profile
│   └── services/
│       └── apiService.js           # API communication
├── App.js                          # Main app component
└── package.json                    # Dependencies
```

## API Integration

The mobile app integrates with the Laravel backend through REST API endpoints:

### Authentication
- `POST /api/login` - User login
- `POST /api/logout` - User logout
- `GET /api/user` - Get current user

### Projects
- `GET /api/projects` - List projects
- `POST /api/projects` - Create project
- `PUT /api/projects/{id}` - Update project
- `DELETE /api/projects/{id}` - Delete project

### Services
- `GET /api/services` - List services
- `POST /api/services` - Create service
- `PUT /api/services/{id}` - Update service
- `DELETE /api/services/{id}` - Delete service

### Dashboard
- `GET /api/dashboard/stats` - Get statistics

## Features in Development

- [ ] Image upload for projects
- [ ] Push notifications
- [ ] Offline data caching
- [ ] Advanced filtering options
- [ ] Bulk operations
- [ ] Export functionality

## Troubleshooting

### Common Issues

1. **API Connection Failed**
   - Check if Laravel backend is running
   - Verify IP address in API configuration
   - Ensure devices are on same network

2. **Login Issues**
   - Verify credentials
   - Check Laravel Sanctum configuration
   - Clear app data and try again

3. **Expo Issues**
   - Clear Expo cache: `expo start -c`
   - Restart Metro bundler
   - Update Expo CLI: `npm install -g @expo/cli`

### Network Configuration

For local development, ensure your mobile device and development machine are on the same network. Find your IP address:

**Windows:**
```bash
ipconfig
```

**macOS/Linux:**
```bash
ifconfig
```

## Building for Production

### Android APK

1. **Configure app.json:**
   ```json
   {
     "expo": {
       "name": "ConstructCo Admin",
       "slug": "constructco-admin",
       "version": "1.0.0",
       "android": {
         "package": "com.constructco.admin"
       }
     }
   }
   ```

2. **Build APK:**
   ```bash
   expo build:android
   ```

### iOS App

1. **Configure app.json:**
   ```json
   {
     "expo": {
       "ios": {
         "bundleIdentifier": "com.constructco.admin"
       }
     }
   }
   ```

2. **Build IPA:**
   ```bash
   expo build:ios
   ```

## Support

For technical support or questions:

- Check the main project documentation
- Review Laravel backend setup
- Ensure all dependencies are installed
- Verify network connectivity

## License

This mobile application is part of the ConstructCo project and follows the same licensing terms as the main project.
