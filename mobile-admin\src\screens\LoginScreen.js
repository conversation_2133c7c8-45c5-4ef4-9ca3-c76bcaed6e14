import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../context/AuthContext';
import { Button, Input, Card } from '../components/ui';
import theme from '../theme';

const LoginScreen = () => {
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('password123');
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const { login } = useAuth();

  const handleLogin = async () => {
    if (!email || !password) {
      Alert.alert('Error', 'Please fill in all fields');
      return;
    }

    setLoading(true);
    try {
      const result = await login(email, password);
      if (!result.success) {
        Alert.alert('Login Failed', result.error);
      }
    } catch (error) {
      Alert.alert('Error', 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <LinearGradient
      colors={theme.colors.gradients.dark}
      style={styles.container}
    >
      <KeyboardAvoidingView
        style={styles.keyboardContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView contentContainerStyle={styles.scrollContainer}>
          <View style={styles.header}>
            <View style={styles.logoContainer}>
              <Ionicons name="construct" size={60} color={theme.colors.primary.main} />
              <Text style={styles.logoText}>ConstructCo</Text>
              <Text style={styles.subtitle}>Admin Panel</Text>
            </View>
          </View>

          <Card
            variant="elevated"
            padding="xl"
            borderRadius="xl"
            style={styles.formCard}
          >
            <Text style={styles.title}>Welcome Back!</Text>
            <Text style={styles.description}>
              Sign in to manage your construction business
            </Text>

            <Input
              label="Email Address"
              placeholder="Enter your email"
              value={email}
              onChangeText={setEmail}
              leftIcon="mail-outline"
              keyboardType="email-address"
              autoCapitalize="none"
              autoCorrect={false}
              variant="outlined"
              required
            />

            <Input
              label="Password"
              placeholder="Enter your password"
              value={password}
              onChangeText={setPassword}
              leftIcon="lock-closed-outline"
              secureTextEntry={true}
              autoCapitalize="none"
              autoCorrect={false}
              variant="outlined"
              required
            />

            <Button
              title="Sign In"
              onPress={handleLogin}
              loading={loading}
              disabled={loading}
              variant="primary"
              size="lg"
              fullWidth
              gradient
              style={styles.loginButton}
            />

            <Card
              variant="flat"
              padding="md"
              borderRadius="md"
              style={styles.demoCredentials}
            >
              <Text style={styles.demoTitle}>Demo Credentials:</Text>
              <Text style={styles.demoText}>Email: <EMAIL></Text>
              <Text style={styles.demoText}>Password: password123</Text>
            </Card>
          </Card>

          <View style={styles.footer}>
            <Text style={styles.footerText}>
              Secure admin access only
            </Text>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  keyboardContainer: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: theme.spacing.xl,
  },
  header: {
    alignItems: 'center',
    marginBottom: theme.spacing['4xl'],
  },
  logoContainer: {
    alignItems: 'center',
  },
  logoText: {
    fontSize: theme.typography.sizes['5xl'],
    fontWeight: theme.typography.weights.bold,
    color: theme.colors.primary.main,
    marginTop: theme.spacing.md,
    fontFamily: theme.typography.fonts.bold,
  },
  subtitle: {
    fontSize: theme.typography.sizes.lg,
    color: theme.colors.secondary.light,
    marginTop: theme.spacing.xs,
    fontFamily: theme.typography.fonts.regular,
  },
  formCard: {
    marginBottom: theme.spacing['3xl'],
    ...theme.shadows.xl,
  },
  title: {
    fontSize: theme.typography.sizes['4xl'],
    fontWeight: theme.typography.weights.bold,
    color: theme.colors.text.primary,
    textAlign: 'center',
    marginBottom: theme.spacing.md,
    fontFamily: theme.typography.fonts.bold,
  },
  description: {
    fontSize: theme.typography.sizes.lg,
    color: theme.colors.text.secondary,
    textAlign: 'center',
    marginBottom: theme.spacing['2xl'],
    fontFamily: theme.typography.fonts.regular,
  },
  loginButton: {
    marginTop: theme.spacing.lg,
    marginBottom: theme.spacing.lg,
  },
  demoCredentials: {
    marginTop: theme.spacing.md,
  },
  demoTitle: {
    fontSize: theme.typography.sizes.sm,
    fontWeight: theme.typography.weights.bold,
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.xs,
    fontFamily: theme.typography.fonts.bold,
  },
  demoText: {
    fontSize: theme.typography.sizes.xs,
    color: theme.colors.text.secondary,
    marginBottom: 2,
    fontFamily: theme.typography.fonts.regular,
  },
  footer: {
    alignItems: 'center',
  },
  footerText: {
    color: theme.colors.secondary.light,
    fontSize: theme.typography.sizes.sm,
    fontFamily: theme.typography.fonts.regular,
  },
});

export default LoginScreen;
