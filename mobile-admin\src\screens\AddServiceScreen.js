import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { apiService } from '../services/apiService';

const AddServiceScreen = ({ navigation }) => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    short_description: '',
    icon: 'construct-outline',
    price_from: '',
    is_featured: false,
    is_active: true,
  });
  const [loading, setLoading] = useState(false);

  const iconOptions = [
    'construct-outline',
    'home-outline',
    'business-outline',
    'hammer-outline',
    'build-outline',
    'settings-outline',
  ];

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const validateForm = () => {
    if (!formData.title.trim()) {
      Alert.alert('Validation Error', 'Service title is required');
      return false;
    }
    if (!formData.description.trim()) {
      Alert.alert('Validation Error', 'Service description is required');
      return false;
    }
    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setLoading(true);
    try {
      const serviceData = {
        ...formData,
        price_from: formData.price_from ? parseFloat(formData.price_from) : null,
      };

      await apiService.createService(serviceData);
      Alert.alert('Success', 'Service created successfully', [
        { text: 'OK', onPress: () => navigation.goBack() }
      ]);
    } catch (error) {
      console.error('Failed to create service:', error);
      Alert.alert('Error', 'Failed to create service. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView style={styles.scrollContainer} showsVerticalScrollIndicator={false}>
        <View style={styles.form}>
          {/* Service Title */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Service Title *</Text>
            <TextInput
              style={styles.input}
              value={formData.title}
              onChangeText={(value) => handleInputChange('title', value)}
              placeholder="Enter service title"
              placeholderTextColor="#999"
            />
          </View>

          {/* Short Description */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Short Description</Text>
            <TextInput
              style={styles.input}
              value={formData.short_description}
              onChangeText={(value) => handleInputChange('short_description', value)}
              placeholder="Brief service summary"
              placeholderTextColor="#999"
              multiline
              numberOfLines={2}
            />
          </View>

          {/* Description */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Full Description *</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={formData.description}
              onChangeText={(value) => handleInputChange('description', value)}
              placeholder="Detailed service description"
              placeholderTextColor="#999"
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
          </View>

          {/* Icon Selection */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Icon</Text>
            <View style={styles.iconGrid}>
              {iconOptions.map((icon) => (
                <TouchableOpacity
                  key={icon}
                  style={[
                    styles.iconOption,
                    formData.icon === icon && styles.iconOptionSelected
                  ]}
                  onPress={() => handleInputChange('icon', icon)}
                >
                  <Ionicons 
                    name={icon} 
                    size={24} 
                    color={formData.icon === icon ? '#fff' : '#FFB703'} 
                  />
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Price */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Starting Price ($)</Text>
            <TextInput
              style={styles.input}
              value={formData.price_from}
              onChangeText={(value) => handleInputChange('price_from', value)}
              placeholder="Starting price (optional)"
              placeholderTextColor="#999"
              keyboardType="numeric"
            />
          </View>

          {/* Toggle Options */}
          <View style={styles.toggleGroup}>
            <TouchableOpacity
              style={styles.toggleOption}
              onPress={() => handleInputChange('is_featured', !formData.is_featured)}
            >
              <View style={styles.toggleRow}>
                <View style={styles.toggleInfo}>
                  <Text style={styles.toggleLabel}>Featured Service</Text>
                  <Text style={styles.toggleDescription}>
                    Display this service prominently on the website
                  </Text>
                </View>
                <View style={[
                  styles.toggle,
                  formData.is_featured && styles.toggleActive
                ]}>
                  {formData.is_featured && (
                    <Ionicons name="checkmark" size={16} color="#fff" />
                  )}
                </View>
              </View>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.toggleOption}
              onPress={() => handleInputChange('is_active', !formData.is_active)}
            >
              <View style={styles.toggleRow}>
                <View style={styles.toggleInfo}>
                  <Text style={styles.toggleLabel}>Active Service</Text>
                  <Text style={styles.toggleDescription}>
                    Make this service available and visible
                  </Text>
                </View>
                <View style={[
                  styles.toggle,
                  formData.is_active && styles.toggleActive
                ]}>
                  {formData.is_active && (
                    <Ionicons name="checkmark" size={16} color="#fff" />
                  )}
                </View>
              </View>
            </TouchableOpacity>
          </View>

          {/* Submit Button */}
          <TouchableOpacity
            style={[styles.submitButton, loading && styles.submitButtonDisabled]}
            onPress={handleSubmit}
            disabled={loading}
          >
            {loading ? (
              <ActivityIndicator color="#023047" size="small" />
            ) : (
              <>
                <Ionicons name="checkmark-circle-outline" size={20} color="#023047" />
                <Text style={styles.submitButtonText}>Create Service</Text>
              </>
            )}
          </TouchableOpacity>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  scrollContainer: {
    flex: 1,
  },
  form: {
    padding: 20,
  },
  inputGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#023047',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#fff',
    color: '#023047',
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  iconGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 10,
  },
  iconOption: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#fff',
    borderWidth: 2,
    borderColor: '#FFB703',
    justifyContent: 'center',
    alignItems: 'center',
  },
  iconOptionSelected: {
    backgroundColor: '#FFB703',
  },
  toggleGroup: {
    marginBottom: 30,
  },
  toggleOption: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 15,
    marginBottom: 10,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  toggleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  toggleInfo: {
    flex: 1,
    marginRight: 15,
  },
  toggleLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#023047',
    marginBottom: 4,
  },
  toggleDescription: {
    fontSize: 14,
    color: '#757575',
  },
  toggle: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#e0e0e0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  toggleActive: {
    backgroundColor: '#FFB703',
    borderColor: '#FFB703',
  },
  submitButton: {
    backgroundColor: '#FFB703',
    borderRadius: 12,
    padding: 15,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#FFB703',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 5,
  },
  submitButtonDisabled: {
    opacity: 0.7,
  },
  submitButtonText: {
    color: '#023047',
    fontSize: 18,
    fontWeight: 'bold',
    marginLeft: 8,
  },
});

export default AddServiceScreen;
