import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
  Alert,
  Animated,
  Dimensions,
  Platform,
  ActivityIndicator,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { apiService } from '../services/apiService';
import { useAuth } from '../context/AuthContext';
import { Card, StatCard, Button } from '../components/ui';
import theme from '../theme';

// Get screen dimensions for responsive design
const { width: screenWidth } = Dimensions.get('window');

const DashboardScreen = ({ navigation }) => {
  const [stats, setStats] = useState({});
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState(null);
  const [fadeAnim] = useState(new Animated.Value(0));
  const { user } = useAuth();

  // Memoized greeting based on time of day
  const greeting = useMemo(() => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 17) return 'Good afternoon';
    return 'Good evening';
  }, []);

  // Memoized user display name
  const displayName = useMemo(() => {
    if (user?.name) {
      const firstName = user.name.split(' ')[0];
      return firstName.length > 12 ? `${firstName.substring(0, 12)}...` : firstName;
    }
    return 'Admin';
  }, [user?.name]);

  useEffect(() => {
    loadDashboardData();
  }, []);

  // Animate fade in after loading
  useEffect(() => {
    if (!loading) {
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }).start();
    }
  }, [loading, fadeAnim]);

  const loadDashboardData = useCallback(async () => {
    try {
      setError(null);
      const data = await apiService.getDashboardStats();
      setStats(data);
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
      setError('Failed to load dashboard data. Please try again.');

      // Show alert only if not refreshing (to avoid multiple alerts)
      if (!refreshing) {
        Alert.alert(
          'Connection Error',
          'Unable to load dashboard data. Please check your internet connection and try again.',
          [
            { text: 'Retry', onPress: () => loadDashboardData() },
            { text: 'Cancel', style: 'cancel' }
          ]
        );
      }
    } finally {
      setLoading(false);
    }
  }, [refreshing]);

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  }, [loadDashboardData]);

  // Enhanced StatCard component is now imported from UI components

  // Enhanced QuickAction with animations and accessibility
  const QuickAction = useCallback(({ title, icon, color, onPress }) => {
    const [scaleAnim] = useState(new Animated.Value(1));

    const handlePressIn = () => {
      Animated.spring(scaleAnim, {
        toValue: 0.95,
        useNativeDriver: true,
      }).start();
    };

    const handlePressOut = () => {
      Animated.spring(scaleAnim, {
        toValue: 1,
        useNativeDriver: true,
      }).start();
    };

    return (
      <Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
        <TouchableOpacity
          style={styles.quickAction}
          onPress={onPress}
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          activeOpacity={0.8}
          accessible={true}
          accessibilityLabel={title}
          accessibilityHint={`Tap to ${title.toLowerCase()}`}
          accessibilityRole="button"
        >
          <View style={[styles.quickActionIcon, { backgroundColor: color }]}>
            <Ionicons name={icon} size={28} color="#fff" />
          </View>
          <Text style={styles.quickActionText}>{title}</Text>
        </TouchableOpacity>
      </Animated.View>
    );
  }, []);

  // Enhanced loading state with better animation
  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <View style={styles.loadingContent}>
          <ActivityIndicator size="large" color="#FFB703" />
          <Ionicons name="construct" size={50} color="#FFB703" style={styles.loadingIcon} />
          <Text style={styles.loadingText}>Loading Dashboard...</Text>
          <Text style={styles.loadingSubtext}>Fetching latest data</Text>
        </View>
      </View>
    );
  }

  // Error state
  if (error && !stats.projects) {
    return (
      <View style={styles.errorContainer}>
        <Ionicons name="warning-outline" size={60} color="#FF6B6B" />
        <Text style={styles.errorTitle}>Connection Error</Text>
        <Text style={styles.errorText}>{error}</Text>
        <TouchableOpacity style={styles.retryButton} onPress={loadDashboardData}>
          <Ionicons name="refresh" size={20} color="#fff" />
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <Animated.View style={[styles.container, { opacity: fadeAnim }]}>
      <ScrollView
        style={styles.scrollContainer}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={['#FFB703']}
            tintColor="#FFB703"
            title="Pull to refresh"
            titleColor="#757575"
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Enhanced Header with Gradient */}
        <LinearGradient
          colors={theme.colors.gradients.dark}
          style={styles.header}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          <View style={styles.headerContent}>
            <View style={styles.headerText}>
              <Text style={styles.greeting}>{greeting},</Text>
              <Text style={styles.userName}>{displayName}</Text>
              {error && (
                <View style={styles.connectionStatus}>
                  <Ionicons name="warning" size={12} color={theme.colors.accent.warning} />
                  <Text style={styles.connectionStatusText}>Limited connectivity</Text>
                </View>
              )}
            </View>
            <TouchableOpacity
              style={styles.headerIcon}
              onPress={() => navigation.navigate('Profile')}
              accessible={true}
              accessibilityLabel="Profile"
              accessibilityHint="Tap to view profile"
            >
              <Ionicons name="person-circle-outline" size={32} color={theme.colors.primary.main} />
            </TouchableOpacity>
          </View>
        </LinearGradient>

        {/* Enhanced Statistics Cards */}
        <View style={styles.statsContainer}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Overview</Text>
            <TouchableOpacity
              style={styles.refreshButton}
              onPress={loadDashboardData}
              disabled={refreshing}
            >
              <Ionicons
                name="refresh"
                size={20}
                color={refreshing ? "#ccc" : "#FFB703"}
              />
            </TouchableOpacity>
          </View>

          <View style={styles.statsGrid}>
            <StatCard
              title="Total Projects"
              value={stats.projects}
              trend={stats.projectsTrend}
              trendDirection={stats.projectsTrend > 0 ? 'up' : stats.projectsTrend < 0 ? 'down' : 'neutral'}
              icon="business-outline"
              color={theme.colors.primary.main}
              onPress={() => navigation.navigate('Projects')}
              variant="outlined"
              gradient={false}
            />

            <StatCard
              title="Services"
              value={stats.services}
              trend={stats.servicesTrend}
              trendDirection={stats.servicesTrend > 0 ? 'up' : stats.servicesTrend < 0 ? 'down' : 'neutral'}
              icon="construct-outline"
              color={theme.colors.secondary.main}
              onPress={() => navigation.navigate('Services')}
              variant="outlined"
              gradient={false}
            />

            <StatCard
              title="Media Files"
              value={stats.media}
              trend={stats.mediaTrend}
              trendDirection={stats.mediaTrend > 0 ? 'up' : stats.mediaTrend < 0 ? 'down' : 'neutral'}
              icon="images-outline"
              color={theme.colors.background.dark}
              onPress={() => navigation.navigate('Media')}
              variant="outlined"
              gradient={false}
            />

            <StatCard
              title="New Messages"
              value={stats.messages}
              trend={stats.messagesTrend}
              trendDirection={stats.messagesTrend > 0 ? 'up' : stats.messagesTrend < 0 ? 'down' : 'neutral'}
              icon="mail-outline"
              color={theme.colors.accent.error}
              onPress={() => navigation.navigate('Messages')}
              variant="outlined"
              gradient={false}
            />
          </View>
        </View>

        {/* Enhanced Quick Actions */}
        <View style={styles.quickActionsContainer}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>

          <View style={styles.quickActionsGrid}>
            <QuickAction
              title="Add Project"
              icon="add-circle-outline"
              color="#FFB703"
              onPress={() => navigation.navigate('AddProject')}
            />

            <QuickAction
              title="Add Service"
              icon="construct"
              color="#8ECAE6"
              onPress={() => navigation.navigate('AddService')}
            />

            <QuickAction
              title="Upload Media"
              icon="cloud-upload-outline"
              color="#023047"
              onPress={() => navigation.navigate('Media')}
            />

            <QuickAction
              title="View Messages"
              icon="chatbubble-ellipses-outline"
              color="#FF6B6B"
              onPress={() => navigation.navigate('Messages')}
            />
          </View>
        </View>

        {/* Enhanced Recent Activity */}
        <View style={styles.recentActivityContainer}>
          <Text style={styles.sectionTitle}>Recent Activity</Text>

          <View style={styles.activityCard}>
            <TouchableOpacity
              style={styles.activityItem}
              onPress={() => navigation.navigate('Projects')}
              accessible={true}
              accessibilityLabel="Projects Management"
            >
              <View style={styles.activityIcon}>
                <Ionicons name="business" size={20} color="#FFB703" />
              </View>
              <View style={styles.activityContent}>
                <Text style={styles.activityTitle}>Projects Management</Text>
                <Text style={styles.activityDescription}>
                  Manage your construction projects, update status, and track progress
                </Text>
              </View>
              <Ionicons name="chevron-forward" size={16} color="#ccc" />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.activityItem}
              onPress={() => navigation.navigate('Services')}
              accessible={true}
              accessibilityLabel="Services Portfolio"
            >
              <View style={styles.activityIcon}>
                <Ionicons name="construct" size={20} color="#8ECAE6" />
              </View>
              <View style={styles.activityContent}>
                <Text style={styles.activityTitle}>Services Portfolio</Text>
                <Text style={styles.activityDescription}>
                  Update your service offerings and pricing information
                </Text>
              </View>
              <Ionicons name="chevron-forward" size={16} color="#ccc" />
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.activityItem, { marginBottom: 0 }]}
              onPress={() => navigation.navigate('Messages')}
              accessible={true}
              accessibilityLabel="Customer Messages"
            >
              <View style={styles.activityIcon}>
                <Ionicons name="mail" size={20} color="#FF6B6B" />
              </View>
              <View style={styles.activityContent}>
                <Text style={styles.activityTitle}>Customer Messages</Text>
                <Text style={styles.activityDescription}>
                  {stats.messages > 0
                    ? `You have ${stats.messages} new message${stats.messages > 1 ? 's' : ''} to review`
                    : 'No new messages at this time'
                  }
                </Text>
              </View>
              <Ionicons name="chevron-forward" size={16} color="#ccc" />
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background.secondary,
  },
  scrollContainer: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.background.secondary,
  },
  loadingContent: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingIcon: {
    marginVertical: theme.spacing.md,
  },
  loadingText: {
    marginTop: theme.spacing.md,
    fontSize: theme.typography.sizes.lg,
    color: theme.colors.text.secondary,
    fontWeight: theme.typography.weights.semibold,
    fontFamily: theme.typography.fonts.medium,
  },
  loadingSubtext: {
    marginTop: theme.spacing.xs,
    fontSize: theme.typography.sizes.sm,
    color: theme.colors.text.disabled,
    fontFamily: theme.typography.fonts.regular,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.background.secondary,
    padding: theme.spacing.xl,
  },
  errorTitle: {
    fontSize: theme.typography.sizes['2xl'],
    fontWeight: theme.typography.weights.bold,
    color: theme.colors.accent.error,
    marginTop: theme.spacing.lg,
    marginBottom: theme.spacing.md,
    fontFamily: theme.typography.fonts.bold,
  },
  errorText: {
    fontSize: theme.typography.sizes.lg,
    color: theme.colors.text.secondary,
    textAlign: 'center',
    marginBottom: theme.spacing.xl,
    lineHeight: theme.typography.lineHeights.relaxed * theme.typography.sizes.lg,
    fontFamily: theme.typography.fonts.regular,
  },
  retryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.primary.main,
    paddingHorizontal: theme.spacing.xl,
    paddingVertical: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
    gap: theme.spacing.sm,
    ...theme.shadows.md,
  },
  retryButtonText: {
    color: theme.colors.text.inverse,
    fontSize: theme.typography.sizes.lg,
    fontWeight: theme.typography.weights.semibold,
    fontFamily: theme.typography.fonts.medium,
  },
  header: {
    padding: theme.spacing.xl,
    paddingTop: theme.spacing['2xl'],
    ...theme.shadows.lg,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerText: {
    flex: 1,
  },
  greeting: {
    fontSize: theme.typography.sizes.lg,
    color: theme.colors.secondary.light,
    fontFamily: theme.typography.fonts.regular,
  },
  userName: {
    fontSize: theme.typography.sizes['3xl'],
    fontWeight: theme.typography.weights.bold,
    color: theme.colors.text.inverse,
    marginTop: theme.spacing.xs,
    fontFamily: theme.typography.fonts.bold,
  },
  connectionStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: theme.spacing.xs,
    gap: theme.spacing.xs,
  },
  connectionStatusText: {
    fontSize: theme.typography.sizes.xs,
    color: theme.colors.accent.warning,
    fontFamily: theme.typography.fonts.regular,
  },
  headerIcon: {
    width: 50,
    height: 50,
    borderRadius: theme.borderRadius.full,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  statsContainer: {
    padding: theme.spacing.xl,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.lg,
  },
  sectionTitle: {
    fontSize: theme.typography.sizes['2xl'],
    fontWeight: theme.typography.weights.bold,
    color: theme.colors.text.primary,
    fontFamily: theme.typography.fonts.bold,
  },
  refreshButton: {
    padding: theme.spacing.sm,
    borderRadius: theme.borderRadius.full,
    backgroundColor: theme.colors.background.primary,
    ...theme.shadows.sm,
  },
  statsGrid: {
    gap: theme.spacing.lg,
  },
  // StatCard styles removed - using new UI component
  quickActionsContainer: {
    padding: theme.spacing.xl,
    paddingTop: 0,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: theme.spacing.lg,
  },
  quickAction: {
    width: screenWidth < 400 ? '100%' : '48%',
    backgroundColor: theme.colors.background.primary,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.xl,
    alignItems: 'center',
    ...theme.shadows.md,
    minHeight: 120,
  },
  quickActionIcon: {
    width: 60,
    height: 60,
    borderRadius: theme.borderRadius.full,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  quickActionText: {
    fontSize: theme.typography.sizes.sm,
    fontWeight: theme.typography.weights.semibold,
    color: theme.colors.text.primary,
    textAlign: 'center',
    fontFamily: theme.typography.fonts.medium,
  },
  recentActivityContainer: {
    padding: theme.spacing.xl,
    paddingTop: 0,
    paddingBottom: theme.spacing['3xl'],
  },
  activityCard: {
    backgroundColor: theme.colors.background.primary,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.xl,
    ...theme.shadows.md,
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.xl,
    paddingVertical: theme.spacing.xs,
  },
  activityIcon: {
    width: 40,
    height: 40,
    borderRadius: theme.borderRadius.full,
    backgroundColor: theme.colors.background.secondary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: theme.spacing.lg,
  },
  activityContent: {
    flex: 1,
  },
  activityTitle: {
    fontSize: theme.typography.sizes.lg,
    fontWeight: theme.typography.weights.semibold,
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.xs,
    fontFamily: theme.typography.fonts.medium,
  },
  activityDescription: {
    fontSize: theme.typography.sizes.sm,
    color: theme.colors.text.secondary,
    lineHeight: theme.typography.lineHeights.relaxed * theme.typography.sizes.sm,
    fontFamily: theme.typography.fonts.regular,
  },
});

export default DashboardScreen;
