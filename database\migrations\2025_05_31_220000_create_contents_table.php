<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('contents', function (Blueprint $table) {
            $table->id();
            $table->string('key')->unique(); // e.g., 'homepage_hero_title', 'about_company_story'
            $table->string('title'); // Human readable title for admin interface
            $table->text('content')->nullable(); // The actual content
            $table->string('type')->default('text'); // text, textarea, html, image, json
            $table->string('page')->default('homepage'); // homepage, about, contact, etc.
            $table->string('section')->nullable(); // hero, services, team, etc.
            $table->text('description')->nullable(); // Help text for admin
            $table->integer('sort_order')->default(0);
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('contents');
    }
};
