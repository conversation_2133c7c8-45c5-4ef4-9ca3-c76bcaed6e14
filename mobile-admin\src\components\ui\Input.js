import React, { useState } from 'react';
import {
  View,
  TextInput,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import theme from '../../theme';

const Input = ({
  label,
  placeholder,
  value,
  onChangeText,
  error,
  helperText,
  leftIcon,
  rightIcon,
  onRightIconPress,
  secureTextEntry = false,
  multiline = false,
  numberOfLines = 1,
  variant = 'outlined',
  size = 'md',
  disabled = false,
  required = false,
  style,
  inputStyle,
  labelStyle,
  ...props
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [labelAnimation] = useState(new Animated.Value(value ? 1 : 0));
  
  const handleFocus = () => {
    setIsFocused(true);
    if (variant === 'floating') {
      Animated.timing(labelAnimation, {
        toValue: 1,
        duration: 200,
        useNativeDriver: false,
      }).start();
    }
  };
  
  const handleBlur = () => {
    setIsFocused(false);
    if (variant === 'floating' && !value) {
      Animated.timing(labelAnimation, {
        toValue: 0,
        duration: 200,
        useNativeDriver: false,
      }).start();
    }
  };
  
  const getContainerStyle = () => {
    const baseStyle = [styles.container];
    
    switch (size) {
      case 'sm':
        baseStyle.push(styles.containerSm);
        break;
      case 'lg':
        baseStyle.push(styles.containerLg);
        break;
      default:
        baseStyle.push(styles.containerMd);
    }
    
    return baseStyle;
  };
  
  const getInputContainerStyle = () => {
    const baseStyle = [styles.inputContainer];
    
    switch (variant) {
      case 'filled':
        baseStyle.push(styles.inputContainerFilled);
        break;
      case 'underlined':
        baseStyle.push(styles.inputContainerUnderlined);
        break;
      case 'floating':
        baseStyle.push(styles.inputContainerFloating);
        break;
      default:
        baseStyle.push(styles.inputContainerOutlined);
    }
    
    if (isFocused) {
      baseStyle.push(styles.inputContainerFocused);
    }
    
    if (error) {
      baseStyle.push(styles.inputContainerError);
    }
    
    if (disabled) {
      baseStyle.push(styles.inputContainerDisabled);
    }
    
    return baseStyle;
  };
  
  const getInputStyle = () => {
    const baseStyle = [styles.input];
    
    switch (size) {
      case 'sm':
        baseStyle.push(styles.inputSm);
        break;
      case 'lg':
        baseStyle.push(styles.inputLg);
        break;
      default:
        baseStyle.push(styles.inputMd);
    }
    
    if (leftIcon) {
      baseStyle.push(styles.inputWithLeftIcon);
    }
    
    if (rightIcon || secureTextEntry) {
      baseStyle.push(styles.inputWithRightIcon);
    }
    
    if (multiline) {
      baseStyle.push(styles.inputMultiline);
    }
    
    return baseStyle;
  };
  
  const getLabelStyle = () => {
    const baseStyle = [styles.label];
    
    if (variant === 'floating') {
      baseStyle.push(styles.labelFloating);
    }
    
    if (error) {
      baseStyle.push(styles.labelError);
    }
    
    if (required) {
      baseStyle.push(styles.labelRequired);
    }
    
    return baseStyle;
  };
  
  const getIconColor = () => {
    if (error) return theme.colors.accent.error;
    if (isFocused) return theme.colors.primary.main;
    return theme.colors.text.secondary;
  };
  
  const renderFloatingLabel = () => {
    if (variant !== 'floating') return null;
    
    return (
      <Animated.Text
        style={[
          getLabelStyle(),
          {
            transform: [
              {
                translateY: labelAnimation.interpolate({
                  inputRange: [0, 1],
                  outputRange: [20, -10],
                }),
              },
              {
                scale: labelAnimation.interpolate({
                  inputRange: [0, 1],
                  outputRange: [1, 0.8],
                }),
              },
            ],
          },
          labelStyle,
        ]}
      >
        {label}
        {required && <Text style={styles.required}> *</Text>}
      </Animated.Text>
    );
  };
  
  const renderStaticLabel = () => {
    if (variant === 'floating' || !label) return null;
    
    return (
      <Text style={[getLabelStyle(), labelStyle]}>
        {label}
        {required && <Text style={styles.required}> *</Text>}
      </Text>
    );
  };
  
  const renderRightIcon = () => {
    if (secureTextEntry) {
      return (
        <TouchableOpacity
          style={styles.iconButton}
          onPress={() => setShowPassword(!showPassword)}
        >
          <Ionicons
            name={showPassword ? 'eye-outline' : 'eye-off-outline'}
            size={20}
            color={getIconColor()}
          />
        </TouchableOpacity>
      );
    }
    
    if (rightIcon) {
      return (
        <TouchableOpacity
          style={styles.iconButton}
          onPress={onRightIconPress}
        >
          <Ionicons
            name={rightIcon}
            size={20}
            color={getIconColor()}
          />
        </TouchableOpacity>
      );
    }
    
    return null;
  };
  
  return (
    <View style={[getContainerStyle(), style]}>
      {renderStaticLabel()}
      
      <View style={getInputContainerStyle()}>
        {renderFloatingLabel()}
        
        {leftIcon && (
          <Ionicons
            name={leftIcon}
            size={20}
            color={getIconColor()}
            style={styles.leftIcon}
          />
        )}
        
        <TextInput
          style={[getInputStyle(), inputStyle]}
          placeholder={variant === 'floating' ? '' : placeholder}
          placeholderTextColor={theme.colors.text.disabled}
          value={value}
          onChangeText={onChangeText}
          onFocus={handleFocus}
          onBlur={handleBlur}
          secureTextEntry={secureTextEntry && !showPassword}
          multiline={multiline}
          numberOfLines={numberOfLines}
          editable={!disabled}
          {...props}
        />
        
        {renderRightIcon()}
      </View>
      
      {(error || helperText) && (
        <Text style={[styles.helperText, error && styles.errorText]}>
          {error || helperText}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: theme.spacing.md,
  },
  containerSm: {
    marginBottom: theme.spacing.sm,
  },
  containerMd: {
    marginBottom: theme.spacing.md,
  },
  containerLg: {
    marginBottom: theme.spacing.lg,
  },
  
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    borderColor: theme.colors.border.medium,
    backgroundColor: theme.colors.background.primary,
    position: 'relative',
  },
  inputContainerOutlined: {
    borderWidth: 2,
  },
  inputContainerFilled: {
    backgroundColor: theme.colors.background.secondary,
    borderWidth: 0,
    borderBottomWidth: 2,
    borderRadius: 0,
    borderBottomColor: theme.colors.border.medium,
  },
  inputContainerUnderlined: {
    backgroundColor: 'transparent',
    borderWidth: 0,
    borderBottomWidth: 1,
    borderRadius: 0,
    borderBottomColor: theme.colors.border.medium,
  },
  inputContainerFloating: {
    paddingTop: theme.spacing.md,
  },
  inputContainerFocused: {
    borderColor: theme.colors.primary.main,
  },
  inputContainerError: {
    borderColor: theme.colors.accent.error,
  },
  inputContainerDisabled: {
    backgroundColor: theme.colors.background.tertiary,
    opacity: 0.6,
  },
  
  input: {
    flex: 1,
    fontFamily: theme.typography.fonts.regular,
    color: theme.colors.text.primary,
    fontSize: theme.typography.sizes.md,
  },
  inputSm: {
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.sm,
    fontSize: theme.typography.sizes.sm,
  },
  inputMd: {
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.md,
    fontSize: theme.typography.sizes.md,
  },
  inputLg: {
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.lg,
    fontSize: theme.typography.sizes.lg,
  },
  inputWithLeftIcon: {
    paddingLeft: theme.spacing.xs,
  },
  inputWithRightIcon: {
    paddingRight: theme.spacing.xs,
  },
  inputMultiline: {
    textAlignVertical: 'top',
    minHeight: 80,
  },
  
  label: {
    fontSize: theme.typography.sizes.sm,
    fontFamily: theme.typography.fonts.medium,
    color: theme.colors.text.secondary,
    marginBottom: theme.spacing.xs,
  },
  labelFloating: {
    position: 'absolute',
    left: theme.spacing.md,
    zIndex: 1,
    backgroundColor: theme.colors.background.primary,
    paddingHorizontal: theme.spacing.xs,
  },
  labelError: {
    color: theme.colors.accent.error,
  },
  labelRequired: {
    fontWeight: theme.typography.weights.medium,
  },
  
  leftIcon: {
    marginLeft: theme.spacing.md,
    marginRight: theme.spacing.sm,
  },
  
  iconButton: {
    padding: theme.spacing.sm,
    marginRight: theme.spacing.sm,
  },
  
  helperText: {
    fontSize: theme.typography.sizes.xs,
    color: theme.colors.text.secondary,
    marginTop: theme.spacing.xs,
    marginLeft: theme.spacing.sm,
  },
  errorText: {
    color: theme.colors.accent.error,
  },
  
  required: {
    color: theme.colors.accent.error,
  },
});

export default Input;
