<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Category;
use App\Models\Project;
use App\Models\Service;
use App\Models\Message;

class SampleDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create categories
        $categories = [
            ['name' => 'Residential', 'slug' => 'residential', 'description' => 'Residential construction projects', 'is_active' => true],
            ['name' => 'Commercial', 'slug' => 'commercial', 'description' => 'Commercial construction projects', 'is_active' => true],
            ['name' => 'Industrial', 'slug' => 'industrial', 'description' => 'Industrial construction projects', 'is_active' => true],
        ];

        foreach ($categories as $category) {
            Category::firstOrCreate(['name' => $category['name']], $category);
        }

        // Create services
        $services = [
            [
                'title' => 'Home Construction',
                'slug' => 'home-construction',
                'short_description' => 'Complete home building services',
                'description' => 'We provide complete home construction services from foundation to finishing.',
                'icon' => 'fas fa-home',
                'price_from' => 50000,
                'is_active' => true,
                'is_featured' => true,
                'sort_order' => 1,
            ],
            [
                'title' => 'Renovation Services',
                'slug' => 'renovation-services',
                'short_description' => 'Professional renovation and remodeling',
                'description' => 'Transform your space with our professional renovation services.',
                'icon' => 'fas fa-tools',
                'price_from' => 10000,
                'is_active' => true,
                'is_featured' => false,
                'sort_order' => 2,
            ],
            [
                'title' => 'Project Management',
                'slug' => 'project-management',
                'short_description' => 'Complete project oversight',
                'description' => 'Professional project management for construction projects of all sizes.',
                'icon' => 'fas fa-clipboard-list',
                'price_from' => 5000,
                'is_active' => true,
                'is_featured' => true,
                'sort_order' => 3,
            ],
        ];

        foreach ($services as $service) {
            Service::firstOrCreate(['slug' => $service['slug']], $service);
        }

        // Create sample projects
        $residential = Category::where('name', 'Residential')->first();
        $commercial = Category::where('name', 'Commercial')->first();

        $projects = [
            [
                'title' => 'Modern Family Home',
                'slug' => 'modern-family-home',
                'short_description' => 'A beautiful modern family home with contemporary design',
                'description' => 'This project involved building a 3-bedroom modern family home with open-plan living, sustainable materials, and energy-efficient systems.',
                'location' => 'Springfield, IL',
                'budget' => 250000,
                'status' => 'completed',
                'category_id' => $residential?->id,
                'start_date' => '2024-01-15',
                'end_date' => '2024-06-30',
                'client_name' => 'John & Jane Smith',
                'is_featured' => true,
                'is_published' => true,
                'progress' => 100,
            ],
            [
                'title' => 'Office Complex Renovation',
                'slug' => 'office-complex-renovation',
                'short_description' => 'Complete renovation of a 5-story office building',
                'description' => 'Full renovation of a commercial office complex including HVAC upgrades, modern interiors, and accessibility improvements.',
                'location' => 'Downtown Chicago, IL',
                'budget' => 500000,
                'status' => 'in_progress',
                'category_id' => $commercial?->id,
                'start_date' => '2024-03-01',
                'end_date' => '2024-12-15',
                'client_name' => 'ABC Corporation',
                'is_featured' => true,
                'is_published' => true,
                'progress' => 65,
            ],
        ];

        foreach ($projects as $project) {
            Project::firstOrCreate(['slug' => $project['slug']], $project);
        }

        // Create sample messages
        $messages = [
            [
                'name' => 'Sarah Johnson',
                'email' => '<EMAIL>',
                'phone' => '555-0123',
                'subject' => 'Kitchen Renovation Inquiry',
                'message' => 'Hi, I\'m interested in getting a quote for a complete kitchen renovation. The space is about 200 sq ft.',
                'status' => 'new',
            ],
            [
                'name' => 'Mike Wilson',
                'email' => '<EMAIL>',
                'phone' => '555-0456',
                'subject' => 'New Home Construction',
                'message' => 'We\'re looking to build a new home on our lot. Can we schedule a consultation?',
                'status' => 'read',
                'read_at' => now()->subDays(1),
            ],
            [
                'name' => 'Lisa Brown',
                'email' => '<EMAIL>',
                'subject' => 'Bathroom Remodel',
                'message' => 'I need help with a master bathroom remodel. What\'s your availability?',
                'status' => 'replied',
                'read_at' => now()->subDays(2),
                'replied_at' => now()->subDays(1),
            ],
        ];

        foreach ($messages as $message) {
            Message::create($message);
        }

        echo "Sample data created successfully!\n";
    }
}
