@extends('layouts.admin')

@section('title', 'Messages - Admin Panel')
@section('page-title', 'Messages Management')

@section('content')
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="d-flex align-items-center">
            <h4 class="mb-0 me-3">Customer Messages</h4>
            <span class="badge bg-primary">{{ $messages->total() }} Total</span>
            @php
                $newCount = $messages->where('status', 'new')->count();
            @endphp
            @if($newCount > 0)
                <span class="badge bg-danger ms-2">{{ $newCount }} New</span>
            @endif
        </div>
    </div>
    <div class="col-lg-4 text-end">
        <div class="btn-group">
            <button class="btn btn-outline-primary" onclick="markAllAsRead()">
                <i class="fas fa-check-double me-2"></i>Mark <PERSON> Read
            </button>
            <button class="btn btn-outline-danger" onclick="deleteSelected()" id="deleteSelectedBtn" style="display: none;">
                <i class="fas fa-trash me-2"></i>Delete Selected
            </button>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="{{ route('admin.messages') }}" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label">Search Messages</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="{{ request('search') }}" placeholder="Search by name, email, or subject...">
            </div>
            <div class="col-md-3">
                <label for="status" class="form-label">Status</label>
                <select class="form-select" id="status" name="status">
                    <option value="">All Status</option>
                    <option value="new" {{ request('status') == 'new' ? 'selected' : '' }}>New</option>
                    <option value="read" {{ request('status') == 'read' ? 'selected' : '' }}>Read</option>
                    <option value="replied" {{ request('status') == 'replied' ? 'selected' : '' }}>Replied</option>
                    <option value="archived" {{ request('status') == 'archived' ? 'selected' : '' }}>Archived</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="sort" class="form-label">Sort By</label>
                <select class="form-select" id="sort" name="sort">
                    <option value="newest" {{ request('sort') == 'newest' ? 'selected' : '' }}>Newest First</option>
                    <option value="oldest" {{ request('sort') == 'oldest' ? 'selected' : '' }}>Oldest First</option>
                    <option value="name" {{ request('sort') == 'name' ? 'selected' : '' }}>Name A-Z</option>
                </select>
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button type="submit" class="btn btn-outline-primary me-2">
                    <i class="fas fa-search"></i>
                </button>
                <a href="{{ route('admin.messages') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-times"></i>
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Messages Table -->
<div class="card">
    <div class="card-body">
        @if($messages->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th width="40">
                                <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                            </th>
                            <th>Contact</th>
                            <th>Subject</th>
                            <th>Message</th>
                            <th>Status</th>
                            <th>Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($messages as $message)
                        <tr class="{{ $message->status == 'new' ? 'table-warning' : '' }}">
                            <td>
                                <input type="checkbox" class="message-checkbox" value="{{ $message->id }}" onchange="updateDeleteButton()">
                            </td>
                            <td>
                                <div>
                                    <strong>{{ $message->name }}</strong>
                                    <br>
                                    <small class="text-muted">{{ $message->email }}</small>
                                    @if($message->phone)
                                        <br>
                                        <small class="text-muted">{{ $message->phone }}</small>
                                    @endif
                                </div>
                            </td>
                            <td>
                                <strong>{{ $message->subject ?: 'No Subject' }}</strong>
                            </td>
                            <td>
                                <div style="max-width: 300px;">
                                    {{ Str::limit($message->message, 100) }}
                                    @if(strlen($message->message) > 100)
                                        <a href="#" onclick="viewMessage({{ $message->id }})" class="text-primary">
                                            Read more...
                                        </a>
                                    @endif
                                </div>
                            </td>
                            <td>
                                @php
                                    $statusColors = [
                                        'new' => 'warning',
                                        'read' => 'info',
                                        'replied' => 'success',
                                        'archived' => 'secondary'
                                    ];
                                @endphp
                                <span class="badge bg-{{ $statusColors[$message->status] ?? 'secondary' }}">
                                    {{ ucfirst($message->status) }}
                                </span>
                            </td>
                            <td>
                                <small class="text-muted">
                                    {{ $message->created_at->format('M d, Y') }}
                                    <br>
                                    {{ $message->created_at->format('h:i A') }}
                                </small>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary" 
                                            onclick="viewMessage({{ $message->id }})"
                                            data-bs-toggle="modal" 
                                            data-bs-target="#viewMessageModal">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-secondary dropdown-toggle" 
                                                type="button" data-bs-toggle="dropdown">
                                            <i class="fas fa-cog"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" href="#" onclick="updateStatus({{ $message->id }}, 'read')">
                                                <i class="fas fa-check me-2"></i>Mark as Read
                                            </a></li>
                                            <li><a class="dropdown-item" href="#" onclick="updateStatus({{ $message->id }}, 'replied')">
                                                <i class="fas fa-reply me-2"></i>Mark as Replied
                                            </a></li>
                                            <li><a class="dropdown-item" href="#" onclick="updateStatus({{ $message->id }}, 'archived')">
                                                <i class="fas fa-archive me-2"></i>Archive
                                            </a></li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li><a class="dropdown-item text-danger" href="#" onclick="deleteMessage({{ $message->id }})">
                                                <i class="fas fa-trash me-2"></i>Delete
                                            </a></li>
                                        </ul>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center mt-4">
                <div>
                    <small class="text-muted">
                        Showing {{ $messages->firstItem() }} to {{ $messages->lastItem() }} 
                        of {{ $messages->total() }} results
                    </small>
                </div>
                <div>
                    {{ $messages->links() }}
                </div>
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-envelope fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No Messages Found</h5>
                <p class="text-muted">Customer messages will appear here when they contact you.</p>
            </div>
        @endif
    </div>
</div>

<!-- View Message Modal -->
<div class="modal fade" id="viewMessageModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Message Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="messageDetails">
                <!-- Message details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-success" id="replyButton" onclick="openEmailClient()">
                    <i class="fas fa-reply me-2"></i>Reply via Email
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
let currentMessageEmail = '';

// Toggle select all checkboxes
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.message-checkbox');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });

    updateDeleteButton();
}

// Update delete button visibility
function updateDeleteButton() {
    const checkboxes = document.querySelectorAll('.message-checkbox:checked');
    const deleteBtn = document.getElementById('deleteSelectedBtn');

    if (checkboxes.length > 0) {
        deleteBtn.style.display = 'inline-block';
    } else {
        deleteBtn.style.display = 'none';
    }
}

// View message details
async function viewMessage(id) {
    try {
        const response = await fetch(`/admin/messages/${id}`);
        const message = await response.json();

        currentMessageEmail = message.email;

        const detailsDiv = document.getElementById('messageDetails');
        detailsDiv.innerHTML = `
            <div class="row g-3">
                <div class="col-md-6">
                    <label class="form-label fw-bold">Name</label>
                    <p class="form-control-plaintext">${message.name}</p>
                </div>
                <div class="col-md-6">
                    <label class="form-label fw-bold">Email</label>
                    <p class="form-control-plaintext">${message.email}</p>
                </div>
                ${message.phone ? `
                <div class="col-md-6">
                    <label class="form-label fw-bold">Phone</label>
                    <p class="form-control-plaintext">${message.phone}</p>
                </div>
                ` : ''}
                <div class="col-md-6">
                    <label class="form-label fw-bold">Date</label>
                    <p class="form-control-plaintext">${new Date(message.created_at).toLocaleString()}</p>
                </div>
                <div class="col-12">
                    <label class="form-label fw-bold">Subject</label>
                    <p class="form-control-plaintext">${message.subject || 'No Subject'}</p>
                </div>
                <div class="col-12">
                    <label class="form-label fw-bold">Message</label>
                    <div class="border rounded p-3 bg-light">
                        ${message.message.replace(/\n/g, '<br>')}
                    </div>
                </div>
                <div class="col-12">
                    <label class="form-label fw-bold">Status</label>
                    <div>
                        <select class="form-select" onchange="updateStatus(${message.id}, this.value)">
                            <option value="new" ${message.status === 'new' ? 'selected' : ''}>New</option>
                            <option value="read" ${message.status === 'read' ? 'selected' : ''}>Read</option>
                            <option value="replied" ${message.status === 'replied' ? 'selected' : ''}>Replied</option>
                            <option value="archived" ${message.status === 'archived' ? 'selected' : ''}>Archived</option>
                        </select>
                    </div>
                </div>
            </div>
        `;

        // Mark as read if it's new
        if (message.status === 'new') {
            updateStatus(message.id, 'read');
        }
    } catch (error) {
        alert('Error loading message details');
    }
}

// Update message status
async function updateStatus(id, status) {
    try {
        const response = await fetch(`/admin/messages/${id}`, {
            method: 'PUT',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ status: status })
        });

        const result = await response.json();

        if (response.ok) {
            location.reload();
        } else {
            alert(result.message || 'Error updating message status');
        }
    } catch (error) {
        alert('Error updating message status');
    }
}

// Delete single message
async function deleteMessage(id) {
    if (!confirm('Are you sure you want to delete this message? This action cannot be undone.')) {
        return;
    }

    try {
        const response = await fetch(`/admin/messages/${id}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();

        if (response.ok) {
            location.reload();
        } else {
            alert(result.message || 'Error deleting message');
        }
    } catch (error) {
        alert('Error deleting message');
    }
}

// Delete selected messages
async function deleteSelected() {
    const checkboxes = document.querySelectorAll('.message-checkbox:checked');
    const ids = Array.from(checkboxes).map(cb => cb.value);

    if (ids.length === 0) {
        alert('Please select messages to delete');
        return;
    }

    if (!confirm(`Are you sure you want to delete ${ids.length} message(s)? This action cannot be undone.`)) {
        return;
    }

    try {
        const response = await fetch('/admin/messages/bulk-delete', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ ids: ids })
        });

        const result = await response.json();

        if (response.ok) {
            location.reload();
        } else {
            alert(result.message || 'Error deleting messages');
        }
    } catch (error) {
        alert('Error deleting messages');
    }
}

// Mark all as read
async function markAllAsRead() {
    if (!confirm('Mark all messages as read?')) {
        return;
    }

    try {
        const response = await fetch('/admin/messages/mark-all-read', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();

        if (response.ok) {
            location.reload();
        } else {
            alert(result.message || 'Error marking messages as read');
        }
    } catch (error) {
        alert('Error marking messages as read');
    }
}

// Open email client for reply
function openEmailClient() {
    if (currentMessageEmail) {
        const subject = encodeURIComponent('Re: Your inquiry');
        const body = encodeURIComponent('Thank you for contacting us.\n\n');
        window.open(`mailto:${currentMessageEmail}?subject=${subject}&body=${body}`);
    }
}
</script>
@endpush
