import React from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import theme from '../../theme';

const Card = ({
  children,
  variant = 'elevated',
  padding = 'md',
  margin = 'none',
  onPress,
  gradient = false,
  gradientColors,
  borderRadius = 'md',
  style,
  ...props
}) => {
  const getCardStyle = () => {
    const baseStyle = [styles.card];
    
    // Variant styles
    switch (variant) {
      case 'flat':
        baseStyle.push(styles.cardFlat);
        break;
      case 'outlined':
        baseStyle.push(styles.cardOutlined);
        break;
      case 'transparent':
        baseStyle.push(styles.cardTransparent);
        break;
      default:
        baseStyle.push(styles.cardElevated);
    }
    
    // Padding styles
    switch (padding) {
      case 'none':
        baseStyle.push(styles.paddingNone);
        break;
      case 'sm':
        baseStyle.push(styles.paddingSm);
        break;
      case 'lg':
        baseStyle.push(styles.paddingLg);
        break;
      case 'xl':
        baseStyle.push(styles.paddingXl);
        break;
      default:
        baseStyle.push(styles.paddingMd);
    }
    
    // Margin styles
    switch (margin) {
      case 'sm':
        baseStyle.push(styles.marginSm);
        break;
      case 'md':
        baseStyle.push(styles.marginMd);
        break;
      case 'lg':
        baseStyle.push(styles.marginLg);
        break;
      case 'xl':
        baseStyle.push(styles.marginXl);
        break;
      default:
        baseStyle.push(styles.marginNone);
    }
    
    // Border radius styles
    switch (borderRadius) {
      case 'none':
        baseStyle.push(styles.borderRadiusNone);
        break;
      case 'sm':
        baseStyle.push(styles.borderRadiusSm);
        break;
      case 'lg':
        baseStyle.push(styles.borderRadiusLg);
        break;
      case 'xl':
        baseStyle.push(styles.borderRadiusXl);
        break;
      case 'full':
        baseStyle.push(styles.borderRadiusFull);
        break;
      default:
        baseStyle.push(styles.borderRadiusMd);
    }
    
    return baseStyle;
  };
  
  const getGradientColors = () => {
    if (gradientColors) return gradientColors;
    return theme.colors.gradients.primary;
  };
  
  const CardContent = () => (
    <View style={[getCardStyle(), style]} {...props}>
      {children}
    </View>
  );
  
  const GradientCard = () => (
    <LinearGradient
      colors={getGradientColors()}
      style={[getCardStyle(), style]}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
      {...props}
    >
      {children}
    </LinearGradient>
  );
  
  if (onPress) {
    return (
      <TouchableOpacity
        onPress={onPress}
        activeOpacity={0.8}
        style={styles.touchableCard}
      >
        {gradient ? <GradientCard /> : <CardContent />}
      </TouchableOpacity>
    );
  }
  
  return gradient ? <GradientCard /> : <CardContent />;
};

const styles = StyleSheet.create({
  card: {
    borderRadius: theme.borderRadius.md,
    overflow: 'hidden',
  },
  
  touchableCard: {
    borderRadius: theme.borderRadius.md,
  },
  
  // Variant styles
  cardElevated: {
    backgroundColor: theme.colors.background.primary,
    ...theme.shadows.md,
  },
  cardFlat: {
    backgroundColor: theme.colors.background.primary,
    borderWidth: 1,
    borderColor: theme.colors.border.light,
  },
  cardOutlined: {
    backgroundColor: 'transparent',
    borderWidth: 2,
    borderColor: theme.colors.border.medium,
  },
  cardTransparent: {
    backgroundColor: 'transparent',
  },
  
  // Padding styles
  paddingNone: {
    padding: 0,
  },
  paddingSm: {
    padding: theme.spacing.sm,
  },
  paddingMd: {
    padding: theme.spacing.lg,
  },
  paddingLg: {
    padding: theme.spacing.xl,
  },
  paddingXl: {
    padding: theme.spacing['2xl'],
  },
  
  // Margin styles
  marginNone: {
    margin: 0,
  },
  marginSm: {
    margin: theme.spacing.sm,
  },
  marginMd: {
    margin: theme.spacing.md,
  },
  marginLg: {
    margin: theme.spacing.lg,
  },
  marginXl: {
    margin: theme.spacing.xl,
  },
  
  // Border radius styles
  borderRadiusNone: {
    borderRadius: theme.borderRadius.none,
  },
  borderRadiusSm: {
    borderRadius: theme.borderRadius.sm,
  },
  borderRadiusMd: {
    borderRadius: theme.borderRadius.md,
  },
  borderRadiusLg: {
    borderRadius: theme.borderRadius.lg,
  },
  borderRadiusXl: {
    borderRadius: theme.borderRadius.xl,
  },
  borderRadiusFull: {
    borderRadius: theme.borderRadius.full,
  },
});

export default Card;
