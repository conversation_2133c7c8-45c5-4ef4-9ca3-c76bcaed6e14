import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { Alert } from 'react-native';
import ContentScreen from '../src/screens/ContentScreen';

// Mock the API service
const mockApiService = {
  getContent: jest.fn(),
  getContentItem: jest.fn(),
  createContent: jest.fn(),
  updateContent: jest.fn(),
  deleteContent: jest.fn(),
};

jest.mock('../src/services/apiService', () => ({
  apiService: mockApiService,
}));

// Mock Alert
const mockAlert = jest.spyOn(Alert, 'alert').mockImplementation(() => { });

// Mock the Picker component
jest.mock('@react-native-picker/picker', () => {
  const { View, Text } = require('react-native');
  return {
    Picker: ({ children, selectedValue, onValueChange, testID }) => (
      <View testID={testID || 'picker'}>
        {children}
      </View>
    ),
  };
});

// Mock Picker.Item separately
jest.mock('@react-native-picker/picker', () => {
  const { View, Text } = require('react-native');
  const MockPicker = ({ children, selectedValue, onValueChange, testID }) => (
    <View testID={testID || 'picker'}>
      {children}
    </View>
  );
  MockPicker.Item = ({ label, value }) => (
    <Text testID={`picker-item-${value}`}>{label}</Text>
  );
  return {
    Picker: MockPicker,
  };
});

// Mock navigation
const mockNavigation = {
  navigate: jest.fn(),
  goBack: jest.fn(),
};

// Sample test data
const mockContentData = [
  {
    id: 1,
    title: 'Homepage Hero Title',
    key: 'homepage_hero_title',
    content: 'Welcome to our website',
    type: 'text',
    page: 'homepage',
    section: 'hero',
    description: 'Main hero title',
    sort_order: 1,
    is_active: true,
  },
  {
    id: 2,
    title: 'About Us Description',
    key: 'about_description',
    content: 'We are a construction company',
    type: 'textarea',
    page: 'about',
    section: 'main',
    description: 'About page description',
    sort_order: 2,
    is_active: true,
  },
];

describe('ContentScreen', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockApiService.getContent.mockResolvedValue({ data: mockContentData });
  });

  describe('Basic Rendering', () => {
    it('should render the screen title', async () => {
      const { getByText } = render(<ContentScreen navigation={mockNavigation} />);

      expect(getByText('Website Content')).toBeTruthy();
    });

    it('should render the add button', async () => {
      const { getByText } = render(<ContentScreen navigation={mockNavigation} />);

      expect(getByText('+ Add')).toBeTruthy();
    });

    it('should render search input', async () => {
      const { getByPlaceholderText } = render(<ContentScreen navigation={mockNavigation} />);

      expect(getByPlaceholderText('Search content...')).toBeTruthy();
    });
  });

  describe('Content Loading', () => {
    it('should load and display content', async () => {
      const { getByText } = render(<ContentScreen navigation={mockNavigation} />);

      await waitFor(() => {
        expect(getByText('Homepage Hero Title')).toBeTruthy();
        expect(getByText('About Us Description')).toBeTruthy();
      });

      expect(mockApiService.getContent).toHaveBeenCalledWith({});
    });

    it('should show item count', async () => {
      const { getByText } = render(<ContentScreen navigation={mockNavigation} />);

      await waitFor(() => {
        expect(getByText('2 items')).toBeTruthy();
      });
    });

    it('should handle API errors', async () => {
      mockApiService.getContent.mockRejectedValue(new Error('Network error'));

      render(<ContentScreen navigation={mockNavigation} />);

      await waitFor(() => {
        expect(mockAlert).toHaveBeenCalledWith(
          'Error',
          expect.stringContaining('Failed to load content')
        );
      });
    });
  });

  describe('Search Functionality', () => {
    it('should update search query', async () => {
      const { getByPlaceholderText } = render(<ContentScreen navigation={mockNavigation} />);

      const searchInput = getByPlaceholderText('Search content...');
      fireEvent.changeText(searchInput, 'homepage');

      expect(searchInput.props.value).toBe('homepage');
    });

    it('should clear search when clear button is pressed', async () => {
      const { getByPlaceholderText, getByText } = render(
        <ContentScreen navigation={mockNavigation} />
      );

      const searchInput = getByPlaceholderText('Search content...');
      fireEvent.changeText(searchInput, 'test search');

      const clearButton = getByText('Clear');
      fireEvent.press(clearButton);

      expect(searchInput.props.value).toBe('');
    });
  });

  describe('Modal Functionality', () => {
    it('should open add modal when add button is pressed', async () => {
      const { getByText } = render(<ContentScreen navigation={mockNavigation} />);

      const addButton = getByText('+ Add');
      fireEvent.press(addButton);

      await waitFor(() => {
        expect(getByText('Add Content')).toBeTruthy();
      });
    });

    it('should show form fields in modal', async () => {
      const { getByText, getByPlaceholderText } = render(
        <ContentScreen navigation={mockNavigation} />
      );

      const addButton = getByText('+ Add');
      fireEvent.press(addButton);

      await waitFor(() => {
        expect(getByText('Title *')).toBeTruthy();
        expect(getByText('Key *')).toBeTruthy();
        expect(getByPlaceholderText('Enter title')).toBeTruthy();
        expect(getByPlaceholderText('e.g., homepage_hero_title')).toBeTruthy();
      });
    });

    it('should close modal when cancel is pressed', async () => {
      const { getByText, queryByText } = render(<ContentScreen navigation={mockNavigation} />);

      const addButton = getByText('+ Add');
      fireEvent.press(addButton);

      await waitFor(() => {
        expect(getByText('Add Content')).toBeTruthy();
      });

      const cancelButton = getByText('Cancel');
      fireEvent.press(cancelButton);

      await waitFor(() => {
        expect(queryByText('Add Content')).toBeFalsy();
      });
    });
  });

  describe('Form Validation', () => {
    it('should show validation error for empty form', async () => {
      const { getByText } = render(<ContentScreen navigation={mockNavigation} />);

      const addButton = getByText('+ Add');
      fireEvent.press(addButton);

      await waitFor(() => {
        expect(getByText('Add Content')).toBeTruthy();
      });

      const saveButton = getByText('Save');
      fireEvent.press(saveButton);

      await waitFor(() => {
        expect(mockAlert).toHaveBeenCalledWith(
          'Validation Error',
          'Please fix the errors in the form'
        );
      });
    });
  });

  describe('CRUD Operations', () => {
    it('should call createContent API when saving new content', async () => {
      mockApiService.createContent.mockResolvedValue({ data: { id: 3 } });

      const { getByText, getByPlaceholderText } = render(
        <ContentScreen navigation={mockNavigation} />
      );

      const addButton = getByText('+ Add');
      fireEvent.press(addButton);

      await waitFor(() => {
        expect(getByText('Add Content')).toBeTruthy();
      });

      const titleInput = getByPlaceholderText('Enter title');
      const keyInput = getByPlaceholderText('e.g., homepage_hero_title');

      fireEvent.changeText(titleInput, 'New Content');
      fireEvent.changeText(keyInput, 'new_content');

      const saveButton = getByText('Save');
      fireEvent.press(saveButton);

      await waitFor(() => {
        expect(mockApiService.createContent).toHaveBeenCalledWith(
          expect.objectContaining({
            title: 'New Content',
            key: 'new_content',
          })
        );
      });
    });

    it('should show success message after creating content', async () => {
      mockApiService.createContent.mockResolvedValue({ data: { id: 3 } });

      const { getByText, getByPlaceholderText } = render(
        <ContentScreen navigation={mockNavigation} />
      );

      const addButton = getByText('+ Add');
      fireEvent.press(addButton);

      const titleInput = getByPlaceholderText('Enter title');
      const keyInput = getByPlaceholderText('e.g., homepage_hero_title');

      fireEvent.changeText(titleInput, 'New Content');
      fireEvent.changeText(keyInput, 'new_content');

      const saveButton = getByText('Save');
      fireEvent.press(saveButton);

      await waitFor(() => {
        expect(mockAlert).toHaveBeenCalledWith(
          'Success',
          'Content created successfully'
        );
      });
    });
  });

  describe('Empty State', () => {
    it('should show empty state when no content', async () => {
      mockApiService.getContent.mockResolvedValue({ data: [] });

      const { getByText } = render(<ContentScreen navigation={mockNavigation} />);

      await waitFor(() => {
        expect(getByText('No content found')).toBeTruthy();
        expect(getByText('Add your first content item to get started')).toBeTruthy();
      });
    });
  });
});
