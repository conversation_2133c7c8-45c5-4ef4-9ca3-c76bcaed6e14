<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Project;
use App\Models\Service;
use App\Models\Media;
use App\Models\Message;
use App\Models\User;
use App\Models\Category;
use App\Models\Content;
use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class AdminController extends Controller
{
    /**
     * Show the admin login form.
     */
    public function loginForm()
    {
        if (Auth::check()) {
            return redirect()->route('admin.dashboard');
        }

        return view('admin.login');
    }

    /**
     * Handle admin login.
     */
    public function login(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required',
        ]);

        if (Auth::attempt($request->only('email', 'password'), $request->boolean('remember'))) {
            $request->session()->regenerate();
            return redirect()->intended(route('admin.dashboard'));
        }

        return back()->withErrors([
            'email' => 'The provided credentials do not match our records.',
        ])->onlyInput('email');
    }

    /**
     * Handle admin logout.
     */
    public function logout(Request $request)
    {
        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect()->route('admin.login');
    }

    /**
     * Show the admin dashboard.
     */
    public function dashboard()
    {
        $stats = [
            'total_projects' => Project::count(),
            'active_projects' => Project::where('status', 'in_progress')->count(),
            'completed_projects' => Project::where('status', 'completed')->count(),
            'total_services' => Service::count(),
            'active_services' => Service::where('is_active', true)->count(),
            'total_media' => Media::count(),
            'new_messages' => Message::where('status', 'new')->count(),
            'total_messages' => Message::count(),
        ];

        $recentProjects = Project::with('category')->latest()->take(5)->get();
        $recentMessages = Message::latest()->take(5)->get();

        return view('admin.dashboard', compact('stats', 'recentProjects', 'recentMessages'));
    }

    /**
     * Show projects management.
     */
    public function projects(Request $request)
    {
        $query = Project::with('category');

        if ($request->has('search')) {
            $query->where('title', 'like', '%' . $request->search . '%');
        }

        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        if ($request->has('category')) {
            $query->where('category_id', $request->category);
        }

        $projects = $query->latest()->paginate(10);

        return view('admin.projects', compact('projects'));
    }

    /**
     * Store a new project.
     */
    public function storeProject(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'category_id' => 'required|exists:categories,id',
            'description' => 'nullable|string',
            'location' => 'nullable|string|max:255',
            'budget' => 'nullable|numeric|min:0',
            'status' => 'required|in:planning,in_progress,completed,on_hold',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $images = [];
        if ($request->hasFile('images')) {
            foreach ($request->file('images') as $image) {
                $filename = time() . '_' . uniqid() . '.' . $image->getClientOriginalExtension();
                $path = $image->storeAs('projects', $filename, 'public');
                $images[] = $path;
            }
        }

        $project = Project::create([
            'title' => $request->title,
            'slug' => Str::slug($request->title),
            'category_id' => $request->category_id,
            'description' => $request->description,
            'location' => $request->location,
            'budget' => $request->budget,
            'status' => $request->status,
            'start_date' => $request->start_date,
            'end_date' => $request->end_date,
            'images' => $images,
            'progress' => 0,
            'is_published' => true,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Project created successfully',
            'project' => $project->load('category')
        ]);
    }

    /**
     * Get project for editing.
     */
    public function editProject(Project $project)
    {
        return response()->json($project->load('category'));
    }

    /**
     * Update a project.
     */
    public function updateProject(Request $request, Project $project)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'category_id' => 'required|exists:categories,id',
            'description' => 'nullable|string',
            'location' => 'nullable|string|max:255',
            'budget' => 'nullable|numeric|min:0',
            'status' => 'required|in:planning,in_progress,completed,on_hold',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'progress' => 'nullable|numeric|min:0|max:100',
            'images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $images = $project->images ?? [];
        if ($request->hasFile('images')) {
            foreach ($request->file('images') as $image) {
                $filename = time() . '_' . uniqid() . '.' . $image->getClientOriginalExtension();
                $path = $image->storeAs('projects', $filename, 'public');
                $images[] = $path;
            }
        }

        $project->update([
            'title' => $request->title,
            'slug' => Str::slug($request->title),
            'category_id' => $request->category_id,
            'description' => $request->description,
            'location' => $request->location,
            'budget' => $request->budget,
            'status' => $request->status,
            'start_date' => $request->start_date,
            'end_date' => $request->end_date,
            'progress' => $request->progress ?? $project->progress,
            'images' => $images,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Project updated successfully',
            'project' => $project->load('category')
        ]);
    }

    /**
     * Delete a project.
     */
    public function destroyProject(Project $project)
    {
        // Delete associated images
        if ($project->images) {
            foreach ($project->images as $image) {
                Storage::disk('public')->delete($image);
            }
        }

        $project->delete();

        return response()->json([
            'success' => true,
            'message' => 'Project deleted successfully'
        ]);
    }

    /**
     * Remove an image from a project.
     */
    public function removeProjectImage(Request $request, Project $project)
    {
        $request->validate([
            'image' => 'required|string'
        ]);

        $images = $project->images ?? [];
        $imageToRemove = $request->image;

        if (($key = array_search($imageToRemove, $images)) !== false) {
            unset($images[$key]);
            Storage::disk('public')->delete($imageToRemove);

            $project->update(['images' => array_values($images)]);

            return response()->json([
                'success' => true,
                'message' => 'Image removed successfully'
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Image not found'
        ], 404);
    }

    /**
     * Show services management.
     */
    public function services(Request $request)
    {
        $query = Service::query();

        if ($request->has('search')) {
            $query->where('title', 'like', '%' . $request->search . '%');
        }

        if ($request->has('status')) {
            $query->where('is_active', $request->status);
        }

        $services = $query->ordered()->paginate(10);

        return view('admin.services', compact('services'));
    }

    /**
     * Store a new service.
     */
    public function storeService(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'short_description' => 'nullable|string',
            'description' => 'nullable|string',
            'price_from' => 'nullable|numeric|min:0',
            'icon' => 'nullable|string|max:100',
            'sort_order' => 'nullable|integer|min:0',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
        ]);

        $imagePath = null;
        if ($request->hasFile('image')) {
            $image = $request->file('image');
            $filename = time() . '_' . uniqid() . '.' . $image->getClientOriginalExtension();
            $imagePath = $image->storeAs('services', $filename, 'public');
        }

        $service = Service::create([
            'title' => $request->title,
            'slug' => Str::slug($request->title),
            'short_description' => $request->short_description,
            'description' => $request->description,
            'price_from' => $request->price_from,
            'icon' => $request->icon,
            'image' => $imagePath,
            'sort_order' => $request->sort_order ?? 0,
            'is_active' => $request->boolean('is_active', true),
            'is_featured' => $request->boolean('is_featured', false),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Service created successfully',
            'service' => $service
        ]);
    }

    /**
     * Get service for editing.
     */
    public function editService(Service $service)
    {
        return response()->json($service);
    }

    /**
     * Update a service.
     */
    public function updateService(Request $request, Service $service)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'short_description' => 'nullable|string',
            'description' => 'nullable|string',
            'price_from' => 'nullable|numeric|min:0',
            'icon' => 'nullable|string|max:100',
            'sort_order' => 'nullable|integer|min:0',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
        ]);

        $imagePath = $service->image;
        if ($request->hasFile('image')) {
            // Delete old image
            if ($service->image) {
                Storage::disk('public')->delete($service->image);
            }

            $image = $request->file('image');
            $filename = time() . '_' . uniqid() . '.' . $image->getClientOriginalExtension();
            $imagePath = $image->storeAs('services', $filename, 'public');
        }

        $service->update([
            'title' => $request->title,
            'slug' => Str::slug($request->title),
            'short_description' => $request->short_description,
            'description' => $request->description,
            'price_from' => $request->price_from,
            'icon' => $request->icon,
            'image' => $imagePath,
            'sort_order' => $request->sort_order ?? $service->sort_order,
            'is_active' => $request->boolean('is_active'),
            'is_featured' => $request->boolean('is_featured'),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Service updated successfully',
            'service' => $service
        ]);
    }

    /**
     * Delete a service.
     */
    public function destroyService(Service $service)
    {
        // Delete associated image
        if ($service->image) {
            Storage::disk('public')->delete($service->image);
        }

        $service->delete();

        return response()->json([
            'success' => true,
            'message' => 'Service deleted successfully'
        ]);
    }

    /**
     * Remove image from a service.
     */
    public function removeServiceImage(Service $service)
    {
        if ($service->image) {
            Storage::disk('public')->delete($service->image);
            $service->update(['image' => null]);

            return response()->json([
                'success' => true,
                'message' => 'Service image removed successfully'
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'No image to remove'
        ], 404);
    }

    /**
     * Show media management.
     */
    public function media(Request $request)
    {
        $query = Media::query();

        if ($request->has('search')) {
            $query->where('original_name', 'like', '%' . $request->search . '%');
        }

        if ($request->has('type')) {
            $query->where('file_type', 'like', $request->type . '%');
        }

        // Apply sorting
        switch ($request->get('sort', 'newest')) {
            case 'oldest':
                $query->oldest();
                break;
            case 'name':
                $query->orderBy('original_name');
                break;
            case 'size':
                $query->orderBy('file_size', 'desc');
                break;
            default:
                $query->latest();
        }

        $media = $query->paginate(12);

        return view('admin.media', compact('media'));
    }

    /**
     * Store new media files.
     */
    public function storeMedia(Request $request)
    {
        $request->validate([
            'files.*' => 'required|file|mimes:jpeg,png,jpg,gif,mp4,mov,avi,pdf,doc,docx|max:20480', // 20MB max
            'alt_text' => 'nullable|string|max:255',
            'description' => 'nullable|string',
        ]);

        $uploadedFiles = [];

        if ($request->hasFile('files')) {
            foreach ($request->file('files') as $file) {
                $originalName = $file->getClientOriginalName();
                $filename = time() . '_' . uniqid() . '.' . $file->getClientOriginalExtension();
                $filePath = $file->storeAs('media', $filename, 'public');

                // Determine file type
                $mimeType = $file->getMimeType();
                $fileType = 'document';
                if (str_starts_with($mimeType, 'image/')) {
                    $fileType = 'image';
                } elseif (str_starts_with($mimeType, 'video/')) {
                    $fileType = 'video';
                }

                $media = Media::create([
                    'title' => pathinfo($originalName, PATHINFO_FILENAME),
                    'original_name' => $originalName,
                    'file_path' => $filePath,
                    'file_name' => $filename,
                    'file_type' => $fileType,
                    'mime_type' => $mimeType,
                    'file_size' => $file->getSize(),
                    'alt_text' => $request->alt_text,
                    'description' => $request->description,
                ]);

                $uploadedFiles[] = $media;
            }
        }

        return response()->json([
            'success' => true,
            'message' => count($uploadedFiles) . ' file(s) uploaded successfully',
            'files' => $uploadedFiles
        ]);
    }

    /**
     * Delete a media file.
     */
    public function destroyMedia(Media $media)
    {
        // Delete the physical file
        Storage::disk('public')->delete($media->file_path);

        // Delete the database record
        $media->delete();

        return response()->json([
            'success' => true,
            'message' => 'Media file deleted successfully'
        ]);
    }

    /**
     * Show messages management.
     */
    public function messages(Request $request)
    {
        $query = Message::query();

        if ($request->has('search')) {
            $query->where(function($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('email', 'like', '%' . $request->search . '%')
                  ->orWhere('subject', 'like', '%' . $request->search . '%')
                  ->orWhere('message', 'like', '%' . $request->search . '%');
            });
        }

        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        // Apply sorting
        switch ($request->get('sort', 'newest')) {
            case 'oldest':
                $query->oldest();
                break;
            case 'name':
                $query->orderBy('name');
                break;
            default:
                $query->latest();
        }

        $messages = $query->paginate(10);

        return view('admin.messages', compact('messages'));
    }

    /**
     * Show a specific message.
     */
    public function showMessage(Message $message)
    {
        return response()->json($message);
    }

    /**
     * Update a message.
     */
    public function updateMessage(Request $request, Message $message)
    {
        $request->validate([
            'status' => 'required|in:new,read,replied,archived',
            'admin_notes' => 'nullable|string',
        ]);

        $updateData = [
            'status' => $request->status,
            'admin_notes' => $request->admin_notes,
        ];

        // Set timestamps based on status
        if ($request->status === 'read' && $message->status === 'new') {
            $updateData['read_at'] = now();
        }

        if ($request->status === 'replied') {
            $updateData['replied_at'] = now();
        }

        $message->update($updateData);

        return response()->json([
            'success' => true,
            'message' => 'Message status updated successfully',
            'data' => $message
        ]);
    }

    /**
     * Delete a message.
     */
    public function destroyMessage(Message $message)
    {
        $message->delete();

        return response()->json([
            'success' => true,
            'message' => 'Message deleted successfully'
        ]);
    }

    /**
     * Bulk delete messages.
     */
    public function bulkDeleteMessages(Request $request)
    {
        $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'integer|exists:messages,id',
        ]);

        $deletedCount = Message::whereIn('id', $request->ids)->delete();

        return response()->json([
            'success' => true,
            'message' => $deletedCount . ' message(s) deleted successfully'
        ]);
    }

    /**
     * Mark all messages as read.
     */
    public function markAllMessagesRead()
    {
        $updatedCount = Message::where('status', 'new')->update([
            'status' => 'read',
            'read_at' => now()
        ]);

        return response()->json([
            'success' => true,
            'message' => $updatedCount . ' message(s) marked as read'
        ]);
    }

    /**
     * Show content management.
     */
    public function content(Request $request)
    {
        $query = Content::query();

        if ($request->has('search')) {
            $query->where(function($q) use ($request) {
                $q->where('title', 'like', '%' . $request->search . '%')
                  ->orWhere('key', 'like', '%' . $request->search . '%');
            });
        }

        if ($request->has('page')) {
            $query->where('page', $request->page);
        }

        if ($request->has('section')) {
            $query->where('section', $request->section);
        }

        $contents = $query->ordered()->paginate(15);

        // Get unique pages and sections for filters
        $pages = Content::select('page')->distinct()->pluck('page');
        $sections = Content::select('section')->distinct()->whereNotNull('section')->pluck('section');

        return view('admin.content', compact('contents', 'pages', 'sections'));
    }

    /**
     * Store a new content item.
     */
    public function storeContent(Request $request)
    {
        $request->validate([
            'key' => 'required|string|max:255|unique:contents,key',
            'title' => 'required|string|max:255',
            'content' => 'nullable|string',
            'type' => 'required|in:text,textarea,html,image,json',
            'page' => 'required|string|max:100',
            'section' => 'nullable|string|max:100',
            'description' => 'nullable|string',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
        ]);

        $content = Content::create([
            'key' => $request->key,
            'title' => $request->title,
            'content' => $request->content,
            'type' => $request->type,
            'page' => $request->page,
            'section' => $request->section,
            'description' => $request->description,
            'sort_order' => $request->sort_order ?? 0,
            'is_active' => $request->boolean('is_active', true),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Content created successfully',
            'content' => $content
        ]);
    }

    /**
     * Get content for editing.
     */
    public function editContent(Content $content)
    {
        return response()->json($content);
    }

    /**
     * Update a content item.
     */
    public function updateContent(Request $request, Content $content)
    {
        $request->validate([
            'key' => 'required|string|max:255|unique:contents,key,' . $content->id,
            'title' => 'required|string|max:255',
            'content' => 'nullable|string',
            'type' => 'required|in:text,textarea,html,image,json',
            'page' => 'required|string|max:100',
            'section' => 'nullable|string|max:100',
            'description' => 'nullable|string',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
        ]);

        $content->update([
            'key' => $request->key,
            'title' => $request->title,
            'content' => $request->content,
            'type' => $request->type,
            'page' => $request->page,
            'section' => $request->section,
            'description' => $request->description,
            'sort_order' => $request->sort_order ?? $content->sort_order,
            'is_active' => $request->boolean('is_active'),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Content updated successfully',
            'content' => $content
        ]);
    }

    /**
     * Delete a content item.
     */
    public function destroyContent(Content $content)
    {
        $content->delete();

        return response()->json([
            'success' => true,
            'message' => 'Content deleted successfully'
        ]);
    }

    /**
     * Bulk update content for a specific page.
     */
    public function bulkUpdateContent(Request $request)
    {
        $request->validate([
            'page' => 'required|string',
            'contents' => 'required|array',
            'contents.*.key' => 'required|string',
            'contents.*.content' => 'nullable|string',
        ]);

        foreach ($request->contents as $contentData) {
            Content::where('key', $contentData['key'])
                ->where('page', $request->page)
                ->update(['content' => $contentData['content']]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Content updated successfully'
        ]);
    }

    /**
     * Show settings management.
     */
    public function settings(Request $request)
    {
        $group = $request->get('group', 'general');

        $settings = Setting::when($group !== 'all', function($query) use ($group) {
            return $query->where('group', $group);
        })->orderBy('group')->orderBy('key')->get();

        $groups = Setting::select('group')->distinct()->pluck('group');

        // For API requests, return JSON
        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'settings' => $settings,
                'groups' => $groups,
                'current_group' => $group
            ]);
        }

        return view('admin.settings', compact('settings', 'groups', 'group'));
    }

    /**
     * Update settings.
     */
    public function updateSettings(Request $request)
    {
        $settings = $request->input('settings', []);

        foreach ($settings as $key => $value) {
            Setting::where('key', $key)->update(['value' => $value]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Settings updated successfully'
        ]);
    }

    /**
     * Store a new setting.
     */
    public function storeSetting(Request $request)
    {
        $request->validate([
            'key' => 'required|string|max:255|unique:settings,key',
            'value' => 'nullable|string',
            'type' => 'required|in:text,textarea,boolean,integer,float,json,image',
            'group' => 'required|string|max:100',
            'description' => 'nullable|string',
        ]);

        $setting = Setting::create([
            'key' => $request->key,
            'value' => $request->value,
            'type' => $request->type,
            'group' => $request->group,
            'description' => $request->description,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Setting created successfully',
            'setting' => $setting
        ]);
    }

    /**
     * Delete a setting.
     */
    public function destroySetting(Setting $setting)
    {
        $setting->delete();

        return response()->json([
            'success' => true,
            'message' => 'Setting deleted successfully'
        ]);
    }

    /**
     * Show profile management.
     */
    public function profile()
    {
        $user = Auth::user();

        // For API requests, return JSON
        if (request()->expectsJson()) {
            return response()->json([
                'success' => true,
                'user' => $user
            ]);
        }

        return view('admin.profile', compact('user'));
    }

    /**
     * Update user profile.
     */
    public function updateProfile(Request $request)
    {
        $user = Auth::user();

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'phone' => 'nullable|string|max:20',
            'bio' => 'nullable|string|max:500',
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $data = [
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'bio' => $request->bio,
        ];

        // Handle avatar upload
        if ($request->hasFile('avatar')) {
            // Delete old avatar
            if ($user->avatar) {
                Storage::disk('public')->delete($user->avatar);
            }

            $avatar = $request->file('avatar');
            $filename = time() . '_' . uniqid() . '.' . $avatar->getClientOriginalExtension();
            $avatarPath = $avatar->storeAs('avatars', $filename, 'public');
            $data['avatar'] = $avatarPath;
        }

        $user->update($data);

        return response()->json([
            'success' => true,
            'message' => 'Profile updated successfully',
            'user' => $user->fresh()
        ]);
    }

    /**
     * Update user password.
     */
    public function updatePassword(Request $request)
    {
        $request->validate([
            'current_password' => 'required',
            'password' => 'required|string|min:8|confirmed',
        ]);

        $user = Auth::user();

        if (!Hash::check($request->current_password, $user->password)) {
            return response()->json([
                'success' => false,
                'message' => 'Current password is incorrect'
            ], 422);
        }

        $user->update([
            'password' => Hash::make($request->password)
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Password updated successfully'
        ]);
    }

    /**
     * Update user preferences.
     */
    public function updatePreferences(Request $request)
    {
        $user = Auth::user();
        $preferences = $request->input('preferences', []);

        $user->preferences = array_merge($user->preferences ?? [], $preferences);
        $user->save();

        return response()->json([
            'success' => true,
            'message' => 'Preferences updated successfully'
        ]);
    }

    /**
     * Remove user avatar.
     */
    public function removeAvatar()
    {
        $user = Auth::user();

        if ($user->avatar) {
            Storage::disk('public')->delete($user->avatar);
            $user->update(['avatar' => null]);

            return response()->json([
                'success' => true,
                'message' => 'Avatar removed successfully'
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'No avatar to remove'
        ], 404);
    }
}
