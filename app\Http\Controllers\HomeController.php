<?php

namespace App\Http\Controllers;

use App\Models\Project;
use App\Models\Service;
use App\Models\Media;
use App\Models\Message;
use App\Models\Setting;
use App\Models\Content;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    /**
     * Display the homepage.
     */
    public function index()
    {
        $featuredProjects = Project::published()->featured()->ordered()->take(6)->get();
        $featuredServices = Service::active()->featured()->ordered()->take(6)->get();
        $featuredMedia = Media::featured()->ordered()->take(8)->get();

        // Get homepage content
        $content = Content::getPageContent('homepage');

        return view('home', compact('featuredProjects', 'featuredServices', 'featuredMedia', 'content'));
    }

    /**
     * Display the about page.
     */
    public function about()
    {
        // Get about page content
        $content = Content::getPageContent('about');

        return view('about', compact('content'));
    }

    /**
     * Display all projects.
     */
    public function projects(Request $request)
    {
        $query = Project::published()->with('category');

        // Filter by category
        if ($request->has('category')) {
            $query->whereHas('category', function($q) use ($request) {
                $q->where('slug', $request->category);
            });
        }

        $projects = $query->ordered()->paginate(12);

        return view('projects', compact('projects'));
    }

    /**
     * Display a single project.
     */
    public function project(Project $project)
    {
        if (!$project->is_published) {
            abort(404);
        }

        $relatedProjects = Project::published()
            ->where('id', '!=', $project->id)
            ->where('category_id', $project->category_id)
            ->take(3)
            ->get();

        return view('project', compact('project', 'relatedProjects'));
    }

    /**
     * Display all services.
     */
    public function services()
    {
        $services = Service::active()->ordered()->get();

        return view('services', compact('services'));
    }

    /**
     * Display a single service.
     */
    public function service(Service $service)
    {
        if (!$service->is_active) {
            abort(404);
        }

        return view('service', compact('service'));
    }

    /**
     * Display the media gallery.
     */
    public function media(Request $request)
    {
        $query = Media::query();

        // Filter by type
        if ($request->has('type') && in_array($request->type, ['image', 'video'])) {
            $query->where('file_type', $request->type);
        }

        $media = $query->ordered()->paginate(16);

        return view('media', compact('media'));
    }

    /**
     * Display the contact page.
     */
    public function contact()
    {
        return view('contact');
    }

    /**
     * Handle contact form submission.
     */
    public function contactSubmit(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'subject' => 'required|string|max:255',
            'message' => 'required|string',
        ]);

        Message::create([
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'subject' => $request->subject,
            'message' => $request->message,
            'status' => 'new',
        ]);

        return redirect()->back()->with('success', 'Thank you for your message. We will get back to you soon!');
    }
}
