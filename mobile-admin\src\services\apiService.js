import axios from 'axios';

// Configure base URL - Update this to your Laravel API URL
// Use ***********:8000 for physical devices on same WiFi
// Use ********:8000 for Android emulator
const BASE_URL = 'http://***********:8000/api'; // Your computer's IP address

class ApiService {
  constructor() {
    this.api = axios.create({
      baseURL: BASE_URL,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    // Request interceptor to add auth token
    this.api.interceptors.request.use(
      (config) => {
        if (this.authToken) {
          config.headers.Authorization = `Bearer ${this.authToken}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor for error handling
    this.api.interceptors.response.use(
      (response) => response.data,
      (error) => {
        if (error.response?.status === 401) {
          // Token expired or invalid
          this.setAuthToken(null);
        }
        return Promise.reject(error);
      }
    );

    this.authToken = null;
  }

  setAuthToken(token) {
    this.authToken = token;
  }

  // Authentication
  async login(email, password) {
    const response = await this.api.post('/login', { email, password });
    return response;
  }

  async logout() {
    const response = await this.api.post('/logout');
    return response;
  }

  async getCurrentUser() {
    const response = await this.api.get('/user');
    return response;
  }

  // Dashboard
  async getDashboardStats() {
    const response = await this.api.get('/dashboard/stats');
    return response;
  }

  // Projects
  async getProjects(params = {}) {
    const response = await this.api.get('/projects', { params });
    return response;
  }

  async getProject(id) {
    const response = await this.api.get(`/projects/${id}`);
    return response;
  }

  async createProject(projectData) {
    const response = await this.api.post('/projects', projectData);
    return response;
  }

  async updateProject(id, projectData) {
    const response = await this.api.post(`/projects/${id}`, projectData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response;
  }

  async deleteProject(id) {
    const response = await this.api.delete(`/projects/${id}`);
    return response;
  }

  // Services
  async getServices(params = {}) {
    const response = await this.api.get('/services', { params });
    return response;
  }

  async getService(id) {
    const response = await this.api.get(`/services/${id}`);
    return response;
  }

  async createService(serviceData) {
    const response = await this.api.post('/services', serviceData);
    return response;
  }

  async updateService(id, serviceData) {
    const response = await this.api.post(`/services/${id}`, serviceData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response;
  }

  async deleteService(id) {
    const response = await this.api.delete(`/services/${id}`);
    return response;
  }

  // Media
  async getMedia(params = {}) {
    const response = await this.api.get('/media', { params });
    return response;
  }

  async uploadMedia(formData) {
    try {
      console.log('API Service: Uploading to:', `${BASE_URL}/media`);
      console.log('API Service: FormData keys:', Array.from(formData.keys()));

      const response = await this.api.post('/media', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        timeout: 30000, // 30 second timeout
      });

      console.log('API Service: Upload successful:', response);
      return response;
    } catch (error) {
      console.error('API Service: Upload failed:', error);
      throw error;
    }
  }

  async updateMedia(id, mediaData) {
    const response = await this.api.put(`/media/${id}`, mediaData);
    return response;
  }

  async deleteMedia(id) {
    const response = await this.api.delete(`/media/${id}`);
    return response;
  }

  // Messages
  async getMessages(params = {}) {
    const response = await this.api.get('/messages', { params });
    return response;
  }

  async getMessage(id) {
    const response = await this.api.get(`/messages/${id}`);
    return response;
  }

  async updateMessage(id, messageData) {
    const response = await this.api.put(`/messages/${id}`, messageData);
    return response;
  }

  async deleteMessage(id) {
    const response = await this.api.delete(`/messages/${id}`);
    return response;
  }

  async bulkDeleteMessages(ids) {
    const response = await this.api.post('/messages/bulk-delete', { ids });
    return response;
  }

  async markAllMessagesRead() {
    const response = await this.api.post('/messages/mark-all-read');
    return response;
  }

  // Content Management
  async getContent(params = {}) {
    const response = await this.api.get('/content', { params });
    return response;
  }

  async getContentItem(id) {
    const response = await this.api.get(`/content/${id}/edit`);
    return response;
  }

  async createContent(contentData) {
    const response = await this.api.post('/content', contentData);
    return response;
  }

  async updateContent(id, contentData) {
    const response = await this.api.post(`/content/${id}`, contentData);
    return response;
  }

  async deleteContent(id) {
    const response = await this.api.delete(`/content/${id}`);
    return response;
  }

  async bulkUpdateContent(pageData) {
    const response = await this.api.post('/content/bulk-update', pageData);
    return response;
  }

  // Settings Management
  async getSettings(params = {}) {
    const response = await this.api.get('/settings', { params });
    return response;
  }

  async updateSettings(settingsData) {
    const response = await this.api.post('/settings', settingsData);
    return response;
  }

  async createSetting(settingData) {
    const response = await this.api.post('/settings/store', settingData);
    return response;
  }

  async deleteSetting(id) {
    const response = await this.api.delete(`/settings/${id}`);
    return response;
  }

  // Profile Management
  async getProfile() {
    const response = await this.api.get('/profile');
    return response;
  }

  async updateProfile(profileData) {
    const response = await this.api.post('/profile', profileData);
    return response;
  }

  async updatePassword(passwordData) {
    const response = await this.api.post('/profile/password', passwordData);
    return response;
  }

  async updatePreferences(preferencesData) {
    const response = await this.api.post('/profile/preferences', preferencesData);
    return response;
  }

  async removeAvatar() {
    const response = await this.api.delete('/profile/avatar');
    return response;
  }

  // Categories
  async getCategories() {
    const response = await this.api.get('/categories');
    return response;
  }

  // Get single project/service for editing
  async getProjectForEdit(id) {
    const response = await this.api.get(`/projects/${id}/edit`);
    return response;
  }

  async getServiceForEdit(id) {
    const response = await this.api.get(`/services/${id}/edit`);
    return response;
  }
}

export const apiService = new ApiService();
