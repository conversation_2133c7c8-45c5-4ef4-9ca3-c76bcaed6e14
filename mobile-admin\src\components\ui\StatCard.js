import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import theme from '../../theme';

const StatCard = ({
  title,
  value,
  icon,
  color = theme.colors.primary.main,
  trend,
  trendDirection,
  onPress,
  gradient = false,
  variant = 'default',
  size = 'md',
  style,
  ...props
}) => {
  const [scaleAnim] = useState(new Animated.Value(1));
  
  const handlePressIn = () => {
    Animated.spring(scaleAnim, {
      toValue: 0.95,
      useNativeDriver: true,
    }).start();
  };
  
  const handlePressOut = () => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      useNativeDriver: true,
    }).start();
  };
  
  const formatValue = (val) => {
    if (val === undefined || val === null) return '0';
    if (typeof val === 'number') {
      if (val >= 1000000) {
        return `${(val / 1000000).toFixed(1)}M`;
      }
      if (val >= 1000) {
        return `${(val / 1000).toFixed(1)}K`;
      }
    }
    return val.toString();
  };
  
  const getCardStyle = () => {
    const baseStyle = [styles.card];
    
    switch (variant) {
      case 'minimal':
        baseStyle.push(styles.cardMinimal);
        break;
      case 'outlined':
        baseStyle.push(styles.cardOutlined);
        break;
      case 'filled':
        baseStyle.push(styles.cardFilled);
        break;
      default:
        baseStyle.push(styles.cardDefault);
    }
    
    switch (size) {
      case 'sm':
        baseStyle.push(styles.cardSm);
        break;
      case 'lg':
        baseStyle.push(styles.cardLg);
        break;
      default:
        baseStyle.push(styles.cardMd);
    }
    
    if (variant === 'outlined') {
      baseStyle.push({ borderLeftColor: color });
    }
    
    return baseStyle;
  };
  
  const getValueStyle = () => {
    const baseStyle = [styles.value];
    
    switch (size) {
      case 'sm':
        baseStyle.push(styles.valueSm);
        break;
      case 'lg':
        baseStyle.push(styles.valueLg);
        break;
      default:
        baseStyle.push(styles.valueMd);
    }
    
    if (variant === 'filled') {
      baseStyle.push(styles.valueInverse);
    }
    
    return baseStyle;
  };
  
  const getTitleStyle = () => {
    const baseStyle = [styles.title];
    
    switch (size) {
      case 'sm':
        baseStyle.push(styles.titleSm);
        break;
      case 'lg':
        baseStyle.push(styles.titleLg);
        break;
      default:
        baseStyle.push(styles.titleMd);
    }
    
    if (variant === 'filled') {
      baseStyle.push(styles.titleInverse);
    }
    
    return baseStyle;
  };
  
  const getIconStyle = () => {
    const baseStyle = [styles.iconContainer];
    
    switch (size) {
      case 'sm':
        baseStyle.push(styles.iconContainerSm);
        break;
      case 'lg':
        baseStyle.push(styles.iconContainerLg);
        break;
      default:
        baseStyle.push(styles.iconContainerMd);
    }
    
    baseStyle.push({ backgroundColor: color });
    
    return baseStyle;
  };
  
  const getIconSize = () => {
    switch (size) {
      case 'sm': return 20;
      case 'lg': return 32;
      default: return 24;
    }
  };
  
  const getTrendColor = () => {
    if (trendDirection === 'up') return theme.colors.accent.success;
    if (trendDirection === 'down') return theme.colors.accent.error;
    return theme.colors.text.secondary;
  };
  
  const getTrendIcon = () => {
    if (trendDirection === 'up') return 'trending-up';
    if (trendDirection === 'down') return 'trending-down';
    return 'remove';
  };
  
  const renderTrend = () => {
    if (!trend && trendDirection !== 'neutral') return null;
    
    return (
      <View style={[styles.trendContainer, { backgroundColor: `${getTrendColor()}15` }]}>
        <Ionicons
          name={getTrendIcon()}
          size={12}
          color={getTrendColor()}
        />
        {trend && (
          <Text style={[styles.trendText, { color: getTrendColor() }]}>
            {Math.abs(trend)}%
          </Text>
        )}
      </View>
    );
  };
  
  const CardContent = () => (
    <View style={[getCardStyle(), style]} {...props}>
      <View style={styles.cardContent}>
        <View style={styles.cardInfo}>
          <View style={styles.valueContainer}>
            <Text style={getValueStyle()}>{formatValue(value)}</Text>
            {renderTrend()}
          </View>
          <Text style={getTitleStyle()}>{title}</Text>
        </View>
        
        <View style={getIconStyle()}>
          <Ionicons
            name={icon}
            size={getIconSize()}
            color={theme.colors.text.inverse}
          />
        </View>
      </View>
    </View>
  );
  
  const GradientCard = () => (
    <LinearGradient
      colors={[color, `${color}CC`]}
      style={[getCardStyle(), style]}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
      {...props}
    >
      <View style={styles.cardContent}>
        <View style={styles.cardInfo}>
          <View style={styles.valueContainer}>
            <Text style={[getValueStyle(), styles.valueInverse]}>
              {formatValue(value)}
            </Text>
            {renderTrend()}
          </View>
          <Text style={[getTitleStyle(), styles.titleInverse]}>{title}</Text>
        </View>
        
        <View style={[getIconStyle(), styles.iconContainerInverse]}>
          <Ionicons
            name={icon}
            size={getIconSize()}
            color={theme.colors.text.inverse}
          />
        </View>
      </View>
    </LinearGradient>
  );
  
  if (onPress) {
    return (
      <Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
        <TouchableOpacity
          onPress={onPress}
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          activeOpacity={0.8}
        >
          {gradient ? <GradientCard /> : <CardContent />}
        </TouchableOpacity>
      </Animated.View>
    );
  }
  
  return gradient ? <GradientCard /> : <CardContent />;
};

const styles = StyleSheet.create({
  card: {
    borderRadius: theme.borderRadius.lg,
    overflow: 'hidden',
  },
  cardDefault: {
    backgroundColor: theme.colors.background.primary,
    ...theme.shadows.md,
  },
  cardMinimal: {
    backgroundColor: theme.colors.background.primary,
    ...theme.shadows.sm,
  },
  cardOutlined: {
    backgroundColor: theme.colors.background.primary,
    borderLeftWidth: 4,
    ...theme.shadows.sm,
  },
  cardFilled: {
    backgroundColor: theme.colors.primary.main,
  },
  
  // Size styles
  cardSm: {
    padding: theme.spacing.md,
  },
  cardMd: {
    padding: theme.spacing.lg,
  },
  cardLg: {
    padding: theme.spacing.xl,
  },
  
  cardContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  
  cardInfo: {
    flex: 1,
  },
  
  valueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.xs,
  },
  
  value: {
    fontFamily: theme.typography.fonts.bold,
    fontWeight: theme.typography.weights.bold,
    color: theme.colors.text.primary,
  },
  valueSm: {
    fontSize: theme.typography.sizes['2xl'],
  },
  valueMd: {
    fontSize: theme.typography.sizes['3xl'],
  },
  valueLg: {
    fontSize: theme.typography.sizes['4xl'],
  },
  valueInverse: {
    color: theme.colors.text.inverse,
  },
  
  title: {
    fontFamily: theme.typography.fonts.medium,
    color: theme.colors.text.secondary,
  },
  titleSm: {
    fontSize: theme.typography.sizes.xs,
  },
  titleMd: {
    fontSize: theme.typography.sizes.sm,
  },
  titleLg: {
    fontSize: theme.typography.sizes.md,
  },
  titleInverse: {
    color: theme.colors.text.inverse,
    opacity: 0.9,
  },
  
  iconContainer: {
    borderRadius: theme.borderRadius.full,
    justifyContent: 'center',
    alignItems: 'center',
  },
  iconContainerSm: {
    width: 40,
    height: 40,
  },
  iconContainerMd: {
    width: 50,
    height: 50,
  },
  iconContainerLg: {
    width: 60,
    height: 60,
  },
  iconContainerInverse: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  
  trendContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.xs,
    paddingVertical: 2,
    borderRadius: theme.borderRadius.sm,
    marginLeft: theme.spacing.sm,
  },
  
  trendText: {
    fontSize: theme.typography.sizes.xs,
    fontWeight: theme.typography.weights.semibold,
    marginLeft: 2,
  },
});

export default StatCard;
