<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Content extends Model
{
    protected $fillable = [
        'key',
        'title',
        'content',
        'type',
        'page',
        'section',
        'description',
        'sort_order',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get content by key.
     */
    public static function getByKey($key, $default = null)
    {
        $content = static::where('key', $key)->where('is_active', true)->first();
        
        if (!$content) {
            return $default;
        }

        return $content->content;
    }

    /**
     * Set content by key.
     */
    public static function setByKey($key, $content, $title = null, $type = 'text', $page = 'homepage', $section = null)
    {
        return static::updateOrCreate(
            ['key' => $key],
            [
                'title' => $title ?: $key,
                'content' => $content,
                'type' => $type,
                'page' => $page,
                'section' => $section,
                'is_active' => true,
            ]
        );
    }

    /**
     * Scope a query to only include active content.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to filter by page.
     */
    public function scopePage($query, $page)
    {
        return $query->where('page', $page);
    }

    /**
     * Scope a query to filter by section.
     */
    public function scopeSection($query, $section)
    {
        return $query->where('section', $section);
    }

    /**
     * Scope a query to order by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('created_at');
    }

    /**
     * Get all content for a specific page grouped by section.
     */
    public static function getPageContent($page)
    {
        return static::active()
            ->page($page)
            ->ordered()
            ->get()
            ->groupBy('section');
    }
}
