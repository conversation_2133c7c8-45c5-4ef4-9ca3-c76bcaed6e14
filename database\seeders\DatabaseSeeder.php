<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Category;
use App\Models\Project;
use App\Models\Service;
use App\Models\Setting;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create admin user
        User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
        ]);

        // Create categories
        $categories = [
            ['name' => 'Residential', 'slug' => 'residential', 'description' => 'Residential construction projects'],
            ['name' => 'Commercial', 'slug' => 'commercial', 'description' => 'Commercial building projects'],
            ['name' => 'Renovation', 'slug' => 'renovation', 'description' => 'Renovation and remodeling projects'],
            ['name' => 'Infrastructure', 'slug' => 'infrastructure', 'description' => 'Infrastructure development projects'],
        ];

        foreach ($categories as $category) {
            Category::create($category);
        }

        // Create services
        $services = [
            [
                'title' => 'Residential Construction',
                'slug' => 'residential-construction',
                'description' => 'Complete residential construction services from foundation to finishing. We build custom homes, townhouses, and residential complexes with attention to detail and quality craftsmanship.',
                'short_description' => 'Custom home building and residential construction services',
                'icon' => 'fas fa-home',
                'price_from' => 150000,
                'is_featured' => true,
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'title' => 'Commercial Building',
                'slug' => 'commercial-building',
                'description' => 'Professional commercial construction services for offices, retail spaces, warehouses, and industrial facilities. We deliver projects on time and within budget.',
                'short_description' => 'Office buildings, retail spaces, and commercial facilities',
                'icon' => 'fas fa-building',
                'price_from' => 500000,
                'is_featured' => true,
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'title' => 'Renovation & Remodeling',
                'slug' => 'renovation-remodeling',
                'description' => 'Transform your existing space with our renovation and remodeling services. Kitchen remodels, bathroom upgrades, home additions, and complete makeovers.',
                'short_description' => 'Kitchen, bathroom, and home renovation services',
                'icon' => 'fas fa-tools',
                'price_from' => 25000,
                'is_featured' => true,
                'is_active' => true,
                'sort_order' => 3,
            ],
            [
                'title' => 'Project Management',
                'slug' => 'project-management',
                'description' => 'Comprehensive project management services to ensure your construction project runs smoothly from start to finish. Coordination, scheduling, and quality control.',
                'short_description' => 'End-to-end construction project management',
                'icon' => 'fas fa-clipboard-list',
                'price_from' => 10000,
                'is_featured' => false,
                'is_active' => true,
                'sort_order' => 4,
            ],
        ];

        foreach ($services as $service) {
            Service::create($service);
        }

        // Create sample projects
        $projects = [
            [
                'title' => 'Modern Family Home',
                'slug' => 'modern-family-home',
                'description' => 'A beautiful 3-bedroom modern family home featuring open-plan living, sustainable materials, and energy-efficient design. This project showcases contemporary architecture with clean lines and large windows.',
                'short_description' => 'Contemporary 3-bedroom family home with modern amenities',
                'location' => 'Suburban Heights, CA',
                'start_date' => '2023-03-01',
                'end_date' => '2023-08-15',
                'client_name' => 'Johnson Family',
                'budget' => 450000,
                'status' => 'completed',
                'category_id' => 1,
                'is_featured' => true,
                'is_published' => true,
                'sort_order' => 1,
            ],
            [
                'title' => 'Downtown Office Complex',
                'slug' => 'downtown-office-complex',
                'description' => 'A state-of-the-art 12-story office complex in the heart of downtown, featuring modern amenities, sustainable design, and flexible workspace solutions for businesses of all sizes.',
                'short_description' => '12-story modern office building with premium amenities',
                'location' => 'Downtown Business District',
                'start_date' => '2022-06-01',
                'end_date' => '2024-02-28',
                'client_name' => 'Metro Development Corp',
                'budget' => 15000000,
                'status' => 'in_progress',
                'category_id' => 2,
                'is_featured' => true,
                'is_published' => true,
                'sort_order' => 2,
            ],
            [
                'title' => 'Historic Home Restoration',
                'slug' => 'historic-home-restoration',
                'description' => 'Careful restoration of a 1920s Victorian home, preserving original architectural details while updating with modern conveniences and safety features.',
                'short_description' => 'Victorian home restoration preserving historic character',
                'location' => 'Historic District',
                'start_date' => '2023-09-01',
                'end_date' => '2024-01-30',
                'client_name' => 'Heritage Foundation',
                'budget' => 280000,
                'status' => 'in_progress',
                'category_id' => 3,
                'is_featured' => true,
                'is_published' => true,
                'sort_order' => 3,
            ],
        ];

        foreach ($projects as $project) {
            Project::create($project);
        }

        // Create settings
        $settings = [
            // General settings
            ['key' => 'company_name', 'value' => 'ConstructCo', 'type' => 'text', 'group' => 'general', 'description' => 'Company name displayed throughout the website'],
            ['key' => 'site_title', 'value' => 'ConstructCo - Professional Construction Services', 'type' => 'text', 'group' => 'general', 'description' => 'Main site title for SEO'],
            ['key' => 'site_description', 'value' => 'Leading construction company providing quality residential and commercial building services.', 'type' => 'textarea', 'group' => 'general', 'description' => 'Site description for SEO'],
            ['key' => 'maintenance_mode', 'value' => '0', 'type' => 'boolean', 'group' => 'general', 'description' => 'Enable maintenance mode'],

            // Contact settings
            ['key' => 'company_email', 'value' => '<EMAIL>', 'type' => 'text', 'group' => 'contact', 'description' => 'Main company email address'],
            ['key' => 'company_phone', 'value' => '(*************', 'type' => 'text', 'group' => 'contact', 'description' => 'Main company phone number'],
            ['key' => 'company_address', 'value' => '123 Construction St, City, State 12345', 'type' => 'text', 'group' => 'contact', 'description' => 'Company physical address'],
            ['key' => 'business_hours', 'value' => 'Monday - Friday: 8:00 AM - 6:00 PM', 'type' => 'text', 'group' => 'contact', 'description' => 'Business operating hours'],

            // Website settings
            ['key' => 'hero_title', 'value' => 'Building Your Dreams with Excellence', 'type' => 'text', 'group' => 'website', 'description' => 'Homepage hero section title'],
            ['key' => 'hero_subtitle', 'value' => 'Professional construction services with over 20 years of experience', 'type' => 'text', 'group' => 'website', 'description' => 'Homepage hero section subtitle'],
            ['key' => 'max_upload_size', 'value' => '10', 'type' => 'integer', 'group' => 'website', 'description' => 'Maximum file upload size in MB'],
            ['key' => 'items_per_page', 'value' => '12', 'type' => 'integer', 'group' => 'website', 'description' => 'Default number of items per page'],

            // Email settings
            ['key' => 'smtp_host', 'value' => 'smtp.gmail.com', 'type' => 'text', 'group' => 'email', 'description' => 'SMTP server hostname'],
            ['key' => 'smtp_port', 'value' => '587', 'type' => 'integer', 'group' => 'email', 'description' => 'SMTP server port'],
            ['key' => 'smtp_encryption', 'value' => 'tls', 'type' => 'text', 'group' => 'email', 'description' => 'SMTP encryption method'],
            ['key' => 'email_notifications', 'value' => '1', 'type' => 'boolean', 'group' => 'email', 'description' => 'Enable email notifications'],

            // Security settings
            ['key' => 'session_timeout', 'value' => '120', 'type' => 'integer', 'group' => 'security', 'description' => 'Session timeout in minutes'],
            ['key' => 'password_min_length', 'value' => '8', 'type' => 'integer', 'group' => 'security', 'description' => 'Minimum password length'],
            ['key' => 'require_email_verification', 'value' => '1', 'type' => 'boolean', 'group' => 'security', 'description' => 'Require email verification for new users'],
            ['key' => 'enable_two_factor', 'value' => '0', 'type' => 'boolean', 'group' => 'security', 'description' => 'Enable two-factor authentication'],

            // Appearance settings
            ['key' => 'theme_color', 'value' => '#FFB703', 'type' => 'text', 'group' => 'appearance', 'description' => 'Primary theme color'],
            ['key' => 'logo_url', 'value' => '', 'type' => 'image', 'group' => 'appearance', 'description' => 'Company logo URL'],
            ['key' => 'favicon_url', 'value' => '', 'type' => 'image', 'group' => 'appearance', 'description' => 'Website favicon URL'],
        ];

        foreach ($settings as $setting) {
            Setting::create($setting);
        }

        // Seed content
        $this->call(ContentSeeder::class);
    }
}
