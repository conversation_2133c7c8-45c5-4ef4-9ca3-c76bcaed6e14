<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Media extends Model
{
    protected $fillable = [
        'title',
        'description',
        'file_path',
        'file_name',
        'file_type',
        'mime_type',
        'file_size',
        'alt_text',
        'metadata',
        'is_featured',
        'sort_order',
        'original_name',
    ];

    protected $casts = [
        'metadata' => 'array',
        'is_featured' => 'boolean',
        'file_size' => 'integer',
    ];

    /**
     * Scope a query to only include images.
     */
    public function scopeImages($query)
    {
        return $query->where('file_type', 'image');
    }

    /**
     * Scope a query to only include videos.
     */
    public function scopeVideos($query)
    {
        return $query->where('file_type', 'video');
    }

    /**
     * Scope a query to only include featured media.
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope a query to order by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('created_at', 'desc');
    }

    /**
     * Get the full URL for the media file.
     */
    public function getUrlAttribute()
    {
        return asset('storage/' . $this->file_path);
    }
}
