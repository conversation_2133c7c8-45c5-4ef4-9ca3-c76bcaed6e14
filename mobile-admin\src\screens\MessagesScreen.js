import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Alert,
  RefreshControl,
  Modal,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { apiService } from '../services/apiService';

const MessagesScreen = () => {
  const [messages, setMessages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedMessage, setSelectedMessage] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [filter, setFilter] = useState('all'); // all, new, read, replied, archived

  useEffect(() => {
    loadMessages();
  }, [filter]);

  const loadMessages = async () => {
    try {
      const params = filter !== 'all' ? { status: filter } : {};
      const response = await apiService.getMessages(params);
      setMessages(response.data || []);
    } catch (error) {
      console.error('Error loading messages:', error);
      Alert.alert('Error', 'Failed to load messages');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadMessages();
  };

  const viewMessage = async (message) => {
    try {
      // Mark as read if it's new
      if (message.status === 'new') {
        await updateMessageStatus(message.id, 'read');
      }

      const response = await apiService.getMessage(message.id);
      setSelectedMessage(response);
      setModalVisible(true);
    } catch (error) {
      console.error('Error loading message details:', error);
      Alert.alert('Error', 'Failed to load message details');
    }
  };

  const updateMessageStatus = async (messageId, status) => {
    try {
      await apiService.updateMessage(messageId, { status });
      loadMessages(); // Refresh the list
    } catch (error) {
      console.error('Error updating message status:', error);
      Alert.alert('Error', 'Failed to update message status');
    }
  };

  const deleteMessage = async (messageId) => {
    Alert.alert(
      'Delete Message',
      'Are you sure you want to delete this message?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await apiService.deleteMessage(messageId);
              Alert.alert('Success', 'Message deleted successfully');
              loadMessages();
            } catch (error) {
              console.error('Error deleting message:', error);
              Alert.alert('Error', 'Failed to delete message');
            }
          },
        },
      ]
    );
  };

  const markAllAsRead = async () => {
    try {
      await apiService.markAllMessagesRead();
      Alert.alert('Success', 'All messages marked as read');
      loadMessages();
    } catch (error) {
      console.error('Error marking messages as read:', error);
      Alert.alert('Error', 'Failed to mark messages as read');
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'new': return '#FF6B6B';
      case 'read': return '#4ECDC4';
      case 'replied': return '#45B7D1';
      case 'archived': return '#96CEB4';
      default: return '#BDC3C7';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'new': return 'mail-unread';
      case 'read': return 'mail-open';
      case 'replied': return 'mail';
      case 'archived': return 'archive';
      default: return 'mail';
    }
  };

  const renderFilterButton = (filterValue, label) => (
    <TouchableOpacity
      style={[
        styles.filterButton,
        filter === filterValue && styles.activeFilterButton
      ]}
      onPress={() => setFilter(filterValue)}
    >
      <Text style={[
        styles.filterButtonText,
        filter === filterValue && styles.activeFilterButtonText
      ]}>
        {label}
      </Text>
    </TouchableOpacity>
  );

  const renderMessageItem = ({ item }) => (
    <TouchableOpacity
      style={[
        styles.messageItem,
        item.status === 'new' && styles.newMessageItem
      ]}
      onPress={() => viewMessage(item)}
    >
      <View style={styles.messageHeader}>
        <View style={styles.messageInfo}>
          <Text style={styles.messageName}>{item.name}</Text>
          <Text style={styles.messageEmail}>{item.email}</Text>
        </View>
        <View style={styles.messageStatus}>
          <Ionicons
            name={getStatusIcon(item.status)}
            size={20}
            color={getStatusColor(item.status)}
          />
        </View>
      </View>

      <Text style={styles.messageSubject}>
        {item.subject || 'No Subject'}
      </Text>

      <Text style={styles.messagePreview} numberOfLines={2}>
        {item.message}
      </Text>

      <View style={styles.messageFooter}>
        <Text style={styles.messageDate}>
          {new Date(item.created_at).toLocaleDateString()}
        </Text>
        <View style={styles.messageActions}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => updateMessageStatus(item.id, 'replied')}
          >
            <Ionicons name="checkmark" size={16} color="#45B7D1" />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => deleteMessage(item.id)}
          >
            <Ionicons name="trash" size={16} color="#FF6B6B" />
          </TouchableOpacity>
        </View>
      </View>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#FFB703" />
        <Text style={styles.loadingText}>Loading messages...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Messages</Text>
        <TouchableOpacity style={styles.markAllButton} onPress={markAllAsRead}>
          <Ionicons name="checkmark-done" size={24} color="#fff" />
        </TouchableOpacity>
      </View>

      {/* Filters */}
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.filtersContainer}
        contentContainerStyle={styles.filtersContent}
      >
        {renderFilterButton('all', 'All')}
        {renderFilterButton('new', 'New')}
        {renderFilterButton('read', 'Read')}
        {renderFilterButton('replied', 'Replied')}
        {renderFilterButton('archived', 'Archived')}
      </ScrollView>

      {/* Messages List */}
      {messages.length > 0 ? (
        <FlatList
          data={messages}
          renderItem={renderMessageItem}
          keyExtractor={(item) => item.id.toString()}
          contentContainerStyle={styles.messagesList}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
        />
      ) : (
        <View style={styles.emptyContainer}>
          <Ionicons name="mail-outline" size={80} color="#ccc" />
          <Text style={styles.emptyTitle}>No Messages</Text>
          <Text style={styles.emptyDescription}>
            {filter === 'all'
              ? 'No customer messages yet'
              : `No ${filter} messages`
            }
          </Text>
        </View>
      )}

      {/* Message Detail Modal */}
      <Modal
        animationType="slide"
        transparent={false}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setModalVisible(false)}>
              <Ionicons name="arrow-back" size={24} color="#023047" />
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Message Details</Text>
            <View style={{ width: 24 }} />
          </View>

          {selectedMessage && (
            <ScrollView style={styles.modalContent}>
              <View style={styles.messageDetailHeader}>
                <Text style={styles.detailName}>{selectedMessage.name}</Text>
                <Text style={styles.detailEmail}>{selectedMessage.email}</Text>
                {selectedMessage.phone && (
                  <Text style={styles.detailPhone}>{selectedMessage.phone}</Text>
                )}
                <Text style={styles.detailDate}>
                  {new Date(selectedMessage.created_at).toLocaleString()}
                </Text>
              </View>

              <View style={styles.messageDetailBody}>
                <Text style={styles.detailSubject}>
                  {selectedMessage.subject || 'No Subject'}
                </Text>
                <Text style={styles.detailMessage}>
                  {selectedMessage.message}
                </Text>
              </View>

              <View style={styles.messageDetailActions}>
                <TouchableOpacity
                  style={[styles.statusButton, { backgroundColor: '#4ECDC4' }]}
                  onPress={() => {
                    updateMessageStatus(selectedMessage.id, 'read');
                    setModalVisible(false);
                  }}
                >
                  <Ionicons name="mail-open" size={20} color="#fff" />
                  <Text style={styles.statusButtonText}>Mark as Read</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[styles.statusButton, { backgroundColor: '#45B7D1' }]}
                  onPress={() => {
                    updateMessageStatus(selectedMessage.id, 'replied');
                    setModalVisible(false);
                  }}
                >
                  <Ionicons name="mail" size={20} color="#fff" />
                  <Text style={styles.statusButtonText}>Mark as Replied</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[styles.statusButton, { backgroundColor: '#96CEB4' }]}
                  onPress={() => {
                    updateMessageStatus(selectedMessage.id, 'archived');
                    setModalVisible(false);
                  }}
                >
                  <Ionicons name="archive" size={20} color="#fff" />
                  <Text style={styles.statusButtonText}>Archive</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[styles.statusButton, { backgroundColor: '#FF6B6B' }]}
                  onPress={() => {
                    deleteMessage(selectedMessage.id);
                    setModalVisible(false);
                  }}
                >
                  <Ionicons name="trash" size={20} color="#fff" />
                  <Text style={styles.statusButtonText}>Delete</Text>
                </TouchableOpacity>
              </View>
            </ScrollView>
          )}
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#023047',
  },
  markAllButton: {
    backgroundColor: '#4ECDC4',
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  filtersContainer: {
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  filtersContent: {
    paddingHorizontal: 20,
    paddingVertical: 15,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 10,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
  },
  activeFilterButton: {
    backgroundColor: '#FFB703',
  },
  filterButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#666',
  },
  activeFilterButtonText: {
    color: '#fff',
  },
  messagesList: {
    padding: 20,
  },
  messageItem: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  newMessageItem: {
    borderLeftWidth: 4,
    borderLeftColor: '#FF6B6B',
  },
  messageHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  messageInfo: {
    flex: 1,
  },
  messageName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#023047',
    marginBottom: 2,
  },
  messageEmail: {
    fontSize: 14,
    color: '#666',
  },
  messageStatus: {
    marginLeft: 10,
  },
  messageSubject: {
    fontSize: 15,
    fontWeight: '600',
    color: '#023047',
    marginBottom: 6,
  },
  messagePreview: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
    marginBottom: 12,
  },
  messageFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  messageDate: {
    fontSize: 12,
    color: '#999',
  },
  messageActions: {
    flexDirection: 'row',
  },
  actionButton: {
    padding: 8,
    marginLeft: 8,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#023047',
    marginTop: 20,
    marginBottom: 10,
  },
  emptyDescription: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  // Modal styles
  modalContainer: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#023047',
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  messageDetailHeader: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
  },
  detailName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#023047',
    marginBottom: 5,
  },
  detailEmail: {
    fontSize: 16,
    color: '#666',
    marginBottom: 5,
  },
  detailPhone: {
    fontSize: 16,
    color: '#666',
    marginBottom: 5,
  },
  detailDate: {
    fontSize: 14,
    color: '#999',
  },
  messageDetailBody: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
  },
  detailSubject: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#023047',
    marginBottom: 15,
  },
  detailMessage: {
    fontSize: 16,
    color: '#333',
    lineHeight: 24,
  },
  messageDetailActions: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
  },
  statusButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
  },
  statusButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    marginLeft: 8,
    fontSize: 16,
  },
});

export default MessagesScreen;
