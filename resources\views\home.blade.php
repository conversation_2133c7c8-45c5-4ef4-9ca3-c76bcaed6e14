@extends('layouts.app')

@section('title', 'Home - Professional Construction Services')
@section('description', 'Leading construction company providing quality residential and commercial building services. Expert craftsmanship, reliable service, and exceptional results.')

@section('content')
<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="display-4 fw-bold mb-4">
                    {{ $content['hero']->where('key', 'homepage_hero_title')->first()->content ?? 'Building Your Dreams with Excellence' }}
                </h1>
                <p class="lead mb-4">
                    {{ $content['hero']->where('key', 'homepage_hero_subtitle')->first()->content ?? 'We are a trusted construction company with over 20 years of experience in delivering quality residential and commercial projects. From concept to completion, we build with precision and care.' }}
                </p>
                <div class="d-flex flex-wrap gap-3">
                    <a href="{{ route('projects') }}" class="btn btn-primary btn-lg">View Our Work</a>
                    <a href="{{ route('contact') }}" class="btn btn-secondary btn-lg">Get Quote</a>
                </div>
            </div>
            <div class="col-lg-6 text-center">
                @php
                    $heroImage = $content['hero']->where('key', 'homepage_hero_image')->first();
                    $imageUrl = $heroImage ? $heroImage->content : 'https://images.unsplash.com/photo-1541888946425-d81bb19240f5?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80';
                @endphp
                <img src="{{ $imageUrl }}"
                     alt="Construction Site" class="img-fluid rounded shadow-lg">
            </div>
        </div>
    </div>
</section>

<!-- Featured Services Section -->
<section class="section-padding bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center mb-5">
                <h2 class="display-5 fw-bold mb-3">
                    {{ $content['services']->where('key', 'homepage_services_title')->first()->content ?? 'Our Services' }}
                </h2>
                <p class="lead text-muted">
                    {{ $content['services']->where('key', 'homepage_services_subtitle')->first()->content ?? 'We offer comprehensive construction services to meet all your building needs' }}
                </p>
            </div>
        </div>
        
        <div class="row g-4">
            @forelse($featuredServices as $service)
            <div class="col-lg-4 col-md-6">
                <div class="card h-100 text-center">
                    @if($service->image)
                        <img src="{{ asset('storage/' . $service->image) }}" class="card-img-top" alt="{{ $service->title }}" style="height: 200px; object-fit: cover;">
                    @endif
                    <div class="card-body">
                        @if($service->icon)
                            <i class="{{ $service->icon }} fa-3x text-primary mb-3"></i>
                        @endif
                        <h5 class="card-title">{{ $service->title }}</h5>
                        <p class="card-text">{{ $service->short_description ?: Str::limit($service->description, 100) }}</p>
                        @if($service->price_from)
                            <p class="text-primary fw-bold">Starting from ${{ number_format($service->price_from, 2) }}</p>
                        @endif
                    </div>
                    <div class="card-footer bg-transparent">
                        <a href="{{ route('service', $service->slug) }}" class="btn btn-outline-primary">Learn More</a>
                    </div>
                </div>
            </div>
            @empty
            <div class="col-12 text-center">
                <p class="text-muted">No featured services available at the moment.</p>
            </div>
            @endforelse
        </div>
        
        <div class="text-center mt-5">
            <a href="{{ route('services') }}" class="btn btn-primary btn-lg">View All Services</a>
        </div>
    </div>
</section>

<!-- Featured Projects Section -->
<section class="section-padding">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center mb-5">
                <h2 class="display-5 fw-bold mb-3">
                    {{ $content['projects']->where('key', 'homepage_projects_title')->first()->content ?? 'Featured Projects' }}
                </h2>
                <p class="lead text-muted">
                    {{ $content['projects']->where('key', 'homepage_projects_subtitle')->first()->content ?? 'Take a look at some of our recent completed projects' }}
                </p>
            </div>
        </div>
        
        <div class="row g-4">
            @forelse($featuredProjects as $project)
            <div class="col-lg-4 col-md-6">
                <div class="card h-100">
                    @if($project->images && count($project->images) > 0)
                        <img src="{{ asset('storage/' . $project->images[0]) }}" class="card-img-top" alt="{{ $project->title }}" style="height: 250px; object-fit: cover;">
                    @else
                        <img src="https://images.unsplash.com/photo-1590725175499-8b8c8b0c8b0c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" 
                             class="card-img-top" alt="{{ $project->title }}" style="height: 250px; object-fit: cover;">
                    @endif
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h5 class="card-title">{{ $project->title }}</h5>
                            @if($project->category)
                                <span class="badge bg-primary">{{ $project->category->name }}</span>
                            @endif
                        </div>
                        <p class="card-text">{{ $project->short_description ?: Str::limit($project->description, 100) }}</p>
                        @if($project->location)
                            <p class="text-muted small"><i class="fas fa-map-marker-alt me-1"></i>{{ $project->location }}</p>
                        @endif
                    </div>
                    <div class="card-footer bg-transparent">
                        <a href="{{ route('project', $project->slug) }}" class="btn btn-outline-primary">View Details</a>
                    </div>
                </div>
            </div>
            @empty
            <div class="col-12 text-center">
                <p class="text-muted">No featured projects available at the moment.</p>
            </div>
            @endforelse
        </div>
        
        <div class="text-center mt-5">
            <a href="{{ route('projects') }}" class="btn btn-primary btn-lg">View All Projects</a>
        </div>
    </div>
</section>

<!-- Why Choose Us Section -->
<section class="section-padding bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center mb-5">
                <h2 class="display-5 fw-bold mb-3">
                    {{ $content['why_choose']->where('key', 'homepage_why_choose_title')->first()->content ?? 'Why Choose Us' }}
                </h2>
                <p class="lead text-muted">
                    {{ $content['why_choose']->where('key', 'homepage_why_choose_subtitle')->first()->content ?? 'We are committed to delivering exceptional construction services' }}
                </p>
            </div>
        </div>
        
        <div class="row g-4">
            <div class="col-lg-3 col-md-6 text-center">
                <div class="mb-3">
                    <i class="fas fa-award fa-3x text-primary"></i>
                </div>
                <h5>20+ Years Experience</h5>
                <p class="text-muted">Two decades of proven expertise in construction industry</p>
            </div>
            
            <div class="col-lg-3 col-md-6 text-center">
                <div class="mb-3">
                    <i class="fas fa-users fa-3x text-primary"></i>
                </div>
                <h5>Expert Team</h5>
                <p class="text-muted">Skilled professionals dedicated to quality workmanship</p>
            </div>
            
            <div class="col-lg-3 col-md-6 text-center">
                <div class="mb-3">
                    <i class="fas fa-clock fa-3x text-primary"></i>
                </div>
                <h5>On-Time Delivery</h5>
                <p class="text-muted">We complete projects on schedule and within budget</p>
            </div>
            
            <div class="col-lg-3 col-md-6 text-center">
                <div class="mb-3">
                    <i class="fas fa-shield-alt fa-3x text-primary"></i>
                </div>
                <h5>Quality Guarantee</h5>
                <p class="text-muted">We stand behind our work with comprehensive warranties</p>
            </div>
        </div>
    </div>
</section>

<!-- Call to Action Section -->
<section class="section-padding" style="background: linear-gradient(135deg, var(--secondary-color) 0%, #034663 100%); color: white;">
    <div class="container text-center">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <h2 class="display-5 fw-bold mb-3">
                    {{ $content['cta']->where('key', 'homepage_cta_title')->first()->content ?? 'Ready to Start Your Project?' }}
                </h2>
                <p class="lead mb-4">
                    {{ $content['cta']->where('key', 'homepage_cta_subtitle')->first()->content ?? 'Contact us today for a free consultation and quote. Let\'s bring your construction vision to life.' }}
                </p>
                <div class="d-flex flex-wrap justify-content-center gap-3">
                    <a href="{{ route('contact') }}" class="btn btn-primary btn-lg">Get Free Quote</a>
                    <a href="tel:+1234567890" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-phone me-2"></i>Call Now
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection
