@extends('layouts.app')

@section('title', $service->name . ' - Service Details')
@section('description', $service->short_description ?: Str::limit($service->description, 160))

@section('content')
<!-- Service Hero Section -->
<section class="service-hero-section">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <nav aria-label="breadcrumb" class="mb-4">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ route('home') }}">Home</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('services') }}">Services</a></li>
                        <li class="breadcrumb-item active" aria-current="page">{{ $service->name }}</li>
                    </ol>
                </nav>
                
                <div class="row align-items-center">
                    <div class="col-lg-8">
                        <div class="d-flex align-items-center mb-3">
                            <h1 class="display-5 fw-bold mb-0 me-3">{{ $service->name }}</h1>
                            @if($service->is_featured)
                                <span class="badge bg-warning fs-6">
                                    <i class="fas fa-star me-1"></i>Featured
                                </span>
                            @endif
                        </div>
                        
                        @if($service->short_description)
                            <p class="lead text-muted">{{ $service->short_description }}</p>
                        @endif
                        
                        <div class="service-quick-info">
                            <div class="row g-3">
                                @if($service->category)
                                    <div class="col-auto">
                                        <span class="badge bg-primary fs-6">
                                            <i class="fas fa-tag me-1"></i>{{ $service->category->name }}
                                        </span>
                                    </div>
                                @endif
                                
                                @if($service->is_active)
                                    <div class="col-auto">
                                        <span class="badge bg-success fs-6">
                                            <i class="fas fa-check-circle me-1"></i>Available
                                        </span>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-4 text-lg-end">
                        @if($service->price)
                            <div class="service-price">
                                <h5 class="text-muted mb-1">Starting From</h5>
                                <h3 class="fw-bold text-primary">${{ number_format($service->price) }}</h3>
                                <small class="text-muted">*Price may vary based on project scope</small>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Service Image -->
@if($service->image)
<section class="service-image-section">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="service-image-container">
                    <img src="{{ asset('storage/' . $service->image) }}" 
                         alt="{{ $service->name }}" 
                         class="img-fluid rounded shadow-lg w-100"
                         style="height: 400px; object-fit: cover;">
                </div>
            </div>
        </div>
    </div>
</section>
@endif

<!-- Service Details Section -->
<section class="section-padding">
    <div class="container">
        <div class="row">
            <div class="col-lg-8">
                <div class="service-content">
                    <h2 class="h3 fw-bold mb-4">Service Overview</h2>
                    <div class="content-text">
                        {!! nl2br(e($service->description)) !!}
                    </div>
                    
                    <!-- Service Features -->
                    @if($service->features)
                        <div class="service-features mt-5">
                            <h3 class="h4 fw-bold mb-4">What's Included</h3>
                            <div class="row g-4">
                                @foreach(json_decode($service->features, true) ?? [] as $feature)
                                    <div class="col-md-6">
                                        <div class="feature-item d-flex align-items-start">
                                            <div class="feature-icon me-3">
                                                <i class="fas fa-check-circle text-success fs-5"></i>
                                            </div>
                                            <div class="feature-content">
                                                <h6 class="fw-bold mb-1">{{ $feature['title'] ?? $feature }}</h6>
                                                @if(isset($feature['description']))
                                                    <p class="text-muted mb-0">{{ $feature['description'] }}</p>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif
                    
                    <!-- Process Steps -->
                    @if($service->process_steps)
                        <div class="service-process mt-5">
                            <h3 class="h4 fw-bold mb-4">Our Process</h3>
                            <div class="process-steps">
                                @foreach(json_decode($service->process_steps, true) ?? [] as $index => $step)
                                    <div class="process-step d-flex align-items-start mb-4">
                                        <div class="step-number me-3">
                                            <span class="badge bg-primary rounded-circle" style="width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;">
                                                {{ $index + 1 }}
                                            </span>
                                        </div>
                                        <div class="step-content">
                                            <h6 class="fw-bold mb-2">{{ $step['title'] ?? 'Step ' . ($index + 1) }}</h6>
                                            <p class="text-muted mb-0">{{ $step['description'] ?? $step }}</p>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="service-sidebar">
                    <!-- Service Details Card -->
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-info-circle me-2"></i>Service Details
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="service-specs">
                                @if($service->category)
                                    <div class="spec-item mb-3">
                                        <strong>Category:</strong>
                                        <span class="float-end">{{ $service->category->name }}</span>
                                    </div>
                                @endif
                                
                                @if($service->duration)
                                    <div class="spec-item mb-3">
                                        <strong>Duration:</strong>
                                        <span class="float-end">{{ $service->duration }}</span>
                                    </div>
                                @endif
                                
                                @if($service->price)
                                    <div class="spec-item mb-3">
                                        <strong>Starting Price:</strong>
                                        <span class="float-end">${{ number_format($service->price) }}</span>
                                    </div>
                                @endif
                                
                                <div class="spec-item">
                                    <strong>Availability:</strong>
                                    <span class="float-end">
                                        <span class="badge {{ $service->is_active ? 'bg-success' : 'bg-secondary' }}">
                                            {{ $service->is_active ? 'Available' : 'Not Available' }}
                                        </span>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Contact CTA -->
                    <div class="card shadow-sm mb-4">
                        <div class="card-body text-center">
                            <h5 class="fw-bold mb-3">Ready to Get Started?</h5>
                            <p class="text-muted mb-4">Contact us today for a free consultation and personalized quote for this service.</p>
                            <div class="d-grid gap-2">
                                <a href="{{ route('contact') }}" class="btn btn-primary">
                                    <i class="fas fa-envelope me-2"></i>Request Quote
                                </a>
                                <a href="tel:+1234567890" class="btn btn-outline-primary">
                                    <i class="fas fa-phone me-2"></i>Call Us Now
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- FAQ or Additional Info -->
                    <div class="card shadow-sm">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-question-circle me-2"></i>Need Help?
                            </h6>
                        </div>
                        <div class="card-body">
                            <p class="text-muted mb-3">Have questions about this service? Our experts are here to help.</p>
                            <div class="d-grid">
                                <a href="{{ route('contact') }}" class="btn btn-outline-secondary btn-sm">
                                    <i class="fas fa-comments me-2"></i>Ask a Question
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Related Services Section -->
<section class="section-padding bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="text-center mb-5">
                    <h2 class="display-6 fw-bold">Other Services</h2>
                    <p class="lead text-muted">Explore our complete range of construction services</p>
                </div>

                <div class="row g-4">
                    @php
                        $otherServices = \App\Models\Service::active()
                            ->where('id', '!=', $service->id)
                            ->take(3)
                            ->get();
                    @endphp

                    @foreach($otherServices as $otherService)
                        <div class="col-lg-4 col-md-6">
                            <div class="card h-100 service-card">
                                @if($otherService->image)
                                    <div class="position-relative overflow-hidden">
                                        <img src="{{ asset('storage/' . $otherService->image) }}"
                                             class="card-img-top" alt="{{ $otherService->name }}"
                                             style="height: 200px; object-fit: cover; transition: transform 0.3s ease;">
                                        @if($otherService->is_featured)
                                            <span class="badge bg-warning position-absolute top-0 end-0 m-3">
                                                <i class="fas fa-star me-1"></i>Featured
                                            </span>
                                        @endif
                                    </div>
                                @else
                                    <div class="position-relative">
                                        <img src="https://images.unsplash.com/photo-1504307651254-35680f356dfd?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                                             class="card-img-top" alt="{{ $otherService->name }}"
                                             style="height: 200px; object-fit: cover;">
                                        @if($otherService->is_featured)
                                            <span class="badge bg-warning position-absolute top-0 end-0 m-3">
                                                <i class="fas fa-star me-1"></i>Featured
                                            </span>
                                        @endif
                                    </div>
                                @endif

                                <div class="card-body d-flex flex-column">
                                    <h5 class="card-title">{{ $otherService->name }}</h5>
                                    <p class="card-text flex-grow-1">
                                        {{ $otherService->short_description ?: Str::limit($otherService->description, 100) }}
                                    </p>

                                    @if($otherService->price)
                                        <div class="service-price mb-3">
                                            <small class="text-muted">Starting from</small>
                                            <h6 class="text-primary fw-bold">${{ number_format($otherService->price) }}</h6>
                                        </div>
                                    @endif
                                </div>

                                <div class="card-footer bg-transparent">
                                    <a href="{{ route('service', $otherService->slug) }}" class="btn btn-outline-primary w-100">
                                        <i class="fas fa-eye me-2"></i>Learn More
                                    </a>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <div class="text-center mt-4">
                    <a href="{{ route('services') }}" class="btn btn-primary">View All Services</a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Call to Action Section -->
<section class="section-padding" style="background: linear-gradient(135deg, var(--secondary-color) 0%, #034663 100%); color: white;">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h2 class="display-6 fw-bold mb-3">Ready to Start Your {{ $service->name }} Project?</h2>
                <p class="lead mb-4">Get in touch with our experts today for a free consultation and detailed quote tailored to your specific needs.</p>
                <div class="d-flex flex-wrap justify-content-center gap-3">
                    <a href="{{ route('contact') }}" class="btn btn-primary btn-lg">
                        <i class="fas fa-envelope me-2"></i>Get Free Quote
                    </a>
                    <a href="tel:+1234567890" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-phone me-2"></i>Call Now
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@push('styles')
<style>
.service-hero-section {
    padding: 2rem 0;
    background: var(--bg-light);
}

.service-image-section {
    padding: 2rem 0;
}

.service-card {
    transition: all 0.3s ease;
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
}

.service-card:hover .card-img-top {
    transform: scale(1.05);
}

.spec-item {
    border-bottom: 1px solid #eee;
    padding-bottom: 0.5rem;
}

.spec-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.content-text {
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--text-dark);
}

.feature-item {
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid var(--primary-color);
}

.process-step {
    position: relative;
}

.process-step:not(:last-child)::after {
    content: '';
    position: absolute;
    left: 19px;
    top: 50px;
    width: 2px;
    height: calc(100% - 20px);
    background: #e9ecef;
}

.breadcrumb {
    background: transparent;
    padding: 0;
}

.breadcrumb-item + .breadcrumb-item::before {
    color: var(--text-muted);
}

.breadcrumb-item a {
    color: var(--primary-color);
    text-decoration: none;
}

.breadcrumb-item a:hover {
    text-decoration: underline;
}
</style>
@endpush
