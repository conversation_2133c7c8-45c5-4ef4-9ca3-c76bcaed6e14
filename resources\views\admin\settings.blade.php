@extends('layouts.admin')

@section('page-title', 'Settings Management')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Application Settings</h5>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addSettingModal">
                        <i class="fas fa-plus me-2"></i>Add Setting
                    </button>
                </div>
                
                <div class="card-body">
                    <!-- Group Filters -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <select class="form-select" id="groupFilter" onchange="filterByGroup()">
                                <option value="all" {{ $group === 'all' ? 'selected' : '' }}>All Groups</option>
                                @foreach($groups as $groupName)
                                    <option value="{{ $groupName }}" {{ $group === $groupName ? 'selected' : '' }}>
                                        {{ ucfirst($groupName) }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-8">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-success" onclick="saveAllSettings()">
                                    <i class="fas fa-save me-2"></i>Save All Changes
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="resetSettings()">
                                    <i class="fas fa-undo me-2"></i>Reset Changes
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Settings Form -->
                    <form id="settingsForm">
                        @csrf
                        @if($settings->count() > 0)
                            @php $currentGroup = null; @endphp
                            @foreach($settings as $setting)
                                @if($currentGroup !== $setting->group)
                                    @if($currentGroup !== null)
                                        </div></div> <!-- Close previous group -->
                                    @endif
                                    @php $currentGroup = $setting->group; @endphp
                                    <div class="settings-group mb-4">
                                        <h6 class="text-primary mb-3">
                                            <i class="fas fa-folder me-2"></i>{{ ucfirst($setting->group) }} Settings
                                        </h6>
                                        <div class="row g-3">
                                @endif

                                <div class="col-md-6">
                                    <div class="setting-item p-3 border rounded">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <label for="setting_{{ $setting->key }}" class="form-label fw-bold">
                                                {{ ucwords(str_replace('_', ' ', $setting->key)) }}
                                            </label>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                    <i class="fas fa-ellipsis-v"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item" href="#" onclick="editSetting('{{ $setting->id }}')">
                                                        <i class="fas fa-edit me-2"></i>Edit
                                                    </a></li>
                                                    <li><a class="dropdown-item text-danger" href="#" onclick="deleteSetting('{{ $setting->id }}')">
                                                        <i class="fas fa-trash me-2"></i>Delete
                                                    </a></li>
                                                </ul>
                                            </div>
                                        </div>

                                        @if($setting->description)
                                            <p class="text-muted small mb-2">{{ $setting->description }}</p>
                                        @endif

                                        @if($setting->type === 'boolean')
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" 
                                                       id="setting_{{ $setting->key }}" 
                                                       name="settings[{{ $setting->key }}]"
                                                       value="1"
                                                       {{ $setting->value ? 'checked' : '' }}>
                                                <label class="form-check-label" for="setting_{{ $setting->key }}">
                                                    {{ $setting->value ? 'Enabled' : 'Disabled' }}
                                                </label>
                                            </div>
                                        @elseif($setting->type === 'textarea')
                                            <textarea class="form-control" 
                                                      id="setting_{{ $setting->key }}" 
                                                      name="settings[{{ $setting->key }}]"
                                                      rows="3">{{ $setting->value }}</textarea>
                                        @elseif($setting->type === 'json')
                                            <textarea class="form-control font-monospace" 
                                                      id="setting_{{ $setting->key }}" 
                                                      name="settings[{{ $setting->key }}]"
                                                      rows="4">{{ $setting->value }}</textarea>
                                            <div class="form-text">Enter valid JSON format</div>
                                        @else
                                            <input type="{{ $setting->type === 'integer' || $setting->type === 'float' ? 'number' : 'text' }}" 
                                                   class="form-control" 
                                                   id="setting_{{ $setting->key }}" 
                                                   name="settings[{{ $setting->key }}]"
                                                   value="{{ $setting->value }}"
                                                   {{ $setting->type === 'float' ? 'step=0.01' : '' }}>
                                        @endif

                                        <div class="mt-2">
                                            <small class="text-muted">
                                                <strong>Key:</strong> <code>{{ $setting->key }}</code> | 
                                                <strong>Type:</strong> {{ $setting->type }}
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                            @if($currentGroup !== null)
                                </div></div> <!-- Close last group -->
                            @endif
                        @else
                            <div class="text-center py-5">
                                <i class="fas fa-cog fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No settings found</h5>
                                <p class="text-muted">Start by adding your first setting using the button above.</p>
                            </div>
                        @endif
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Setting Modal -->
<div class="modal fade" id="addSettingModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Setting</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addSettingForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="add_key" class="form-label">Key *</label>
                        <input type="text" class="form-control" id="add_key" name="key" required>
                        <div class="form-text">Unique identifier (e.g., site_name, max_upload_size)</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="add_value" class="form-label">Value</label>
                        <input type="text" class="form-control" id="add_value" name="value">
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="add_type" class="form-label">Type *</label>
                                <select class="form-select" id="add_type" name="type" required>
                                    <option value="text">Text</option>
                                    <option value="textarea">Textarea</option>
                                    <option value="boolean">Boolean</option>
                                    <option value="integer">Integer</option>
                                    <option value="float">Float</option>
                                    <option value="json">JSON</option>
                                    <option value="image">Image</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="add_group" class="form-label">Group *</label>
                                <select class="form-select" id="add_group" name="group" required>
                                    <option value="general">General</option>
                                    <option value="website">Website</option>
                                    <option value="email">Email</option>
                                    <option value="security">Security</option>
                                    <option value="appearance">Appearance</option>
                                    <option value="api">API</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="add_description" class="form-label">Description</label>
                        <textarea class="form-control" id="add_description" name="description" rows="2"></textarea>
                        <div class="form-text">Help text for administrators</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Setting</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Filter settings by group
function filterByGroup() {
    const group = document.getElementById('groupFilter').value;
    const url = new URL(window.location);
    url.searchParams.set('group', group);
    window.location.href = url.toString();
}

// Save all settings
async function saveAllSettings() {
    const form = document.getElementById('settingsForm');
    const formData = new FormData(form);

    try {
        const response = await fetch('{{ route("admin.settings.update") }}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });

        const result = await response.json();

        if (response.ok) {
            showAlert('success', result.message);
        } else {
            showAlert('error', result.message || 'Error saving settings');
        }
    } catch (error) {
        showAlert('error', 'Error saving settings');
    }
}

// Reset settings form
function resetSettings() {
    document.getElementById('settingsForm').reset();
    showAlert('info', 'Settings form reset');
}

// Add new setting
document.getElementById('addSettingForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Adding...';
    submitBtn.disabled = true;

    const formData = new FormData(this);

    try {
        const response = await fetch('{{ route("admin.settings.store") }}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });

        const result = await response.json();

        if (response.ok) {
            bootstrap.Modal.getInstance(document.getElementById('addSettingModal')).hide();
            location.reload();
        } else {
            showAlert('error', result.message || 'Error adding setting');
        }
    } catch (error) {
        showAlert('error', 'Error adding setting');
    } finally {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }
});

// Delete setting
async function deleteSetting(id) {
    if (!confirm('Are you sure you want to delete this setting?')) {
        return;
    }

    try {
        const response = await fetch(`/admin/settings/${id}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });

        const result = await response.json();

        if (response.ok) {
            location.reload();
        } else {
            showAlert('error', result.message || 'Error deleting setting');
        }
    } catch (error) {
        showAlert('error', 'Error deleting setting');
    }
}

// Show alert
function showAlert(type, message) {
    const alertClass = type === 'success' ? 'alert-success' :
                      type === 'error' ? 'alert-danger' : 'alert-info';

    const alert = document.createElement('div');
    alert.className = `alert ${alertClass} alert-dismissible fade show`;
    alert.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    const container = document.querySelector('.container-fluid');
    container.insertBefore(alert, container.firstChild);

    // Auto-hide after 5 seconds
    setTimeout(() => {
        if (alert.parentNode) {
            alert.remove();
        }
    }, 5000);
}

// Auto-generate key from input
document.getElementById('add_key').addEventListener('input', function() {
    this.value = this.value.toLowerCase()
        .replace(/[^a-z0-9\s]/g, '')
        .replace(/\s+/g, '_');
});

// Update boolean switch labels
document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
    checkbox.addEventListener('change', function() {
        const label = this.nextElementSibling;
        if (label && label.classList.contains('form-check-label')) {
            label.textContent = this.checked ? 'Enabled' : 'Disabled';
        }
    });
});
</script>
@endpush
