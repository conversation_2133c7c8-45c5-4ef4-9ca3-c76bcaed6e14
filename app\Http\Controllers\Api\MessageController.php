<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Message;
use Illuminate\Http\Request;

class MessageController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Message::query();

        // Filter by status
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        $messages = $query->orderBy('created_at', 'desc')->paginate(10);

        return response()->json($messages);
    }

    /**
     * Store a newly created resource in storage (Contact form submission).
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'subject' => 'required|string|max:255',
            'message' => 'required|string',
        ]);

        $message = Message::create([
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'subject' => $request->subject,
            'message' => $request->message,
            'status' => 'new',
        ]);

        return response()->json([
            'message' => 'Thank you for your message. We will get back to you soon!',
            'data' => $message
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(Message $message)
    {
        // Mark as read when viewed
        if ($message->status === 'new') {
            $message->markAsRead();
        }

        return response()->json($message);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Message $message)
    {
        $request->validate([
            'status' => 'required|in:new,read,replied,archived',
            'admin_notes' => 'nullable|string',
        ]);

        $updateData = [
            'status' => $request->status,
            'admin_notes' => $request->admin_notes,
        ];

        // Set timestamps based on status
        if ($request->status === 'read' && $message->status === 'new') {
            $updateData['read_at'] = now();
        }

        if ($request->status === 'replied') {
            $updateData['replied_at'] = now();
        }

        $message->update($updateData);

        return response()->json($message);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Message $message)
    {
        $message->delete();

        return response()->json([
            'message' => 'Message deleted successfully'
        ]);
    }

    /**
     * Bulk delete messages.
     */
    public function bulkDelete(Request $request)
    {
        $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'integer|exists:messages,id',
        ]);

        $deletedCount = Message::whereIn('id', $request->ids)->delete();

        return response()->json([
            'message' => $deletedCount . ' message(s) deleted successfully'
        ]);
    }

    /**
     * Mark all messages as read.
     */
    public function markAllRead()
    {
        $updatedCount = Message::where('status', 'new')->update([
            'status' => 'read',
            'read_at' => now()
        ]);

        return response()->json([
            'message' => $updatedCount . ' message(s) marked as read'
        ]);
    }
}
