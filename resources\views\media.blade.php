@extends('layouts.app')

@section('title', 'Media Gallery - Our Work in Pictures')
@section('description', 'Browse our comprehensive media gallery showcasing our construction projects, behind-the-scenes work, and company highlights.')

@section('content')
<!-- Media Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h1 class="display-4 fw-bold mb-4">Media Gallery</h1>
                <p class="lead">Explore our collection of project photos, videos, and behind-the-scenes content showcasing our construction expertise and craftsmanship.</p>
            </div>
        </div>
    </div>
</section>

<!-- Filter Section -->
<section class="filter-section">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="card shadow-sm">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h5 class="mb-0">Filter Media</h5>
                            </div>
                            <div class="col-md-6">
                                <form method="GET" action="{{ route('media') }}">
                                    <div class="input-group">
                                        <select name="type" class="form-select">
                                            <option value="">All Media</option>
                                            <option value="image" {{ request('type') == 'image' ? 'selected' : '' }}>Images</option>
                                            <option value="video" {{ request('type') == 'video' ? 'selected' : '' }}>Videos</option>
                                        </select>
                                        <button class="btn btn-primary" type="submit">
                                            <i class="fas fa-filter me-1"></i>Filter
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Media Grid -->
<section class="section-padding">
    <div class="container">
        @if($media->count() > 0)
            <div class="row g-4" id="mediaGrid">
                @foreach($media as $item)
                    <div class="col-lg-3 col-md-4 col-sm-6 media-item" data-type="{{ $item->file_type }}">
                        <div class="media-card">
                            @if($item->file_type === 'image')
                                <div class="media-thumbnail" onclick="openLightbox('{{ asset('storage/' . $item->file_path) }}', '{{ $item->title }}', 'image')">
                                    <img src="{{ asset('storage/' . $item->file_path) }}" 
                                         alt="{{ $item->title }}" 
                                         class="img-fluid w-100"
                                         style="height: 250px; object-fit: cover; cursor: pointer;">
                                    <div class="media-overlay">
                                        <div class="media-overlay-content">
                                            <i class="fas fa-search-plus fa-2x text-white"></i>
                                        </div>
                                    </div>
                                </div>
                            @elseif($item->file_type === 'video')
                                <div class="media-thumbnail" onclick="openLightbox('{{ asset('storage/' . $item->file_path) }}', '{{ $item->title }}', 'video')">
                                    @if($item->thumbnail)
                                        <img src="{{ asset('storage/' . $item->thumbnail) }}" 
                                             alt="{{ $item->title }}" 
                                             class="img-fluid w-100"
                                             style="height: 250px; object-fit: cover; cursor: pointer;">
                                    @else
                                        <div class="video-placeholder d-flex align-items-center justify-content-center"
                                             style="height: 250px; background: #f8f9fa; cursor: pointer;">
                                            <i class="fas fa-play-circle fa-4x text-primary"></i>
                                        </div>
                                    @endif
                                    <div class="media-overlay">
                                        <div class="media-overlay-content">
                                            <i class="fas fa-play fa-2x text-white"></i>
                                        </div>
                                    </div>
                                </div>
                            @else
                                <div class="media-thumbnail">
                                    <div class="file-placeholder d-flex align-items-center justify-content-center flex-column"
                                         style="height: 250px; background: #f8f9fa;">
                                        <i class="fas fa-file fa-3x text-muted mb-2"></i>
                                        <span class="text-muted">{{ strtoupper(pathinfo($item->file_path, PATHINFO_EXTENSION)) }}</span>
                                    </div>
                                </div>
                            @endif
                            
                            <div class="media-info p-3">
                                <h6 class="media-title mb-2">{{ $item->title ?: 'Untitled' }}</h6>
                                @if($item->description)
                                    <p class="media-description text-muted mb-2">{{ Str::limit($item->description, 80) }}</p>
                                @endif
                                <div class="media-meta">
                                    <small class="text-muted">
                                        <i class="fas fa-calendar me-1"></i>{{ $item->created_at->format('M d, Y') }}
                                    </small>
                                    @if($item->file_size)
                                        <small class="text-muted ms-3">
                                            <i class="fas fa-file me-1"></i>{{ number_format($item->file_size / 1024, 1) }} KB
                                        </small>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
            
            <!-- Pagination -->
            @if($media->hasPages())
                <div class="row mt-5">
                    <div class="col-12">
                        <nav aria-label="Media pagination">
                            {{ $media->links() }}
                        </nav>
                    </div>
                </div>
            @endif
        @else
            <div class="row">
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="fas fa-images fa-4x text-muted mb-3"></i>
                        <h3 class="text-muted">No Media Found</h3>
                        <p class="text-muted">We haven't added any media yet, or no media matches your current filter.</p>
                        @if(request('type'))
                            <a href="{{ route('media') }}" class="btn btn-primary">View All Media</a>
                        @endif
                    </div>
                </div>
            </div>
        @endif
    </div>
</section>

<!-- Lightbox Modal -->
<div class="modal fade" id="lightboxModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered">
        <div class="modal-content bg-transparent border-0">
            <div class="modal-header border-0">
                <h5 class="modal-title text-white" id="lightboxTitle"></h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <img id="lightboxImage" src="" alt="" class="img-fluid d-none">
                <video id="lightboxVideo" controls class="w-100 d-none" style="max-height: 80vh;">
                    <source src="" type="video/mp4">
                    Your browser does not support the video tag.
                </video>
            </div>
        </div>
    </div>
</div>

<!-- Call to Action Section -->
<section class="section-padding bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h2 class="display-6 fw-bold mb-3">Want to See Your Project Here?</h2>
                <p class="lead mb-4">Let us help you create something amazing that you'll be proud to showcase. Contact us today to discuss your construction project.</p>
                <div class="d-flex flex-wrap justify-content-center gap-3">
                    <a href="{{ route('contact') }}" class="btn btn-primary btn-lg">
                        <i class="fas fa-envelope me-2"></i>Start Your Project
                    </a>
                    <a href="{{ route('projects') }}" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-eye me-2"></i>View Our Projects
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@push('scripts')
<script>
function openLightbox(src, title, type) {
    const modal = new bootstrap.Modal(document.getElementById('lightboxModal'));
    const lightboxTitle = document.getElementById('lightboxTitle');
    const lightboxImage = document.getElementById('lightboxImage');
    const lightboxVideo = document.getElementById('lightboxVideo');

    // Set title
    lightboxTitle.textContent = title;

    if (type === 'image') {
        // Show image, hide video
        lightboxImage.src = src;
        lightboxImage.alt = title;
        lightboxImage.classList.remove('d-none');
        lightboxVideo.classList.add('d-none');
        lightboxVideo.pause();
    } else if (type === 'video') {
        // Show video, hide image
        lightboxVideo.querySelector('source').src = src;
        lightboxVideo.load();
        lightboxVideo.classList.remove('d-none');
        lightboxImage.classList.add('d-none');
    }

    modal.show();
}

// Close video when modal is hidden
document.getElementById('lightboxModal').addEventListener('hidden.bs.modal', function () {
    const lightboxVideo = document.getElementById('lightboxVideo');
    lightboxVideo.pause();
    lightboxVideo.currentTime = 0;
});

// Media grid animations
document.addEventListener('DOMContentLoaded', function() {
    const mediaItems = document.querySelectorAll('.media-item');

    // Add stagger animation
    mediaItems.forEach((item, index) => {
        item.style.animationDelay = `${index * 0.1}s`;
        item.classList.add('fade-in-up');
    });
});
</script>
@endpush

@push('styles')
<style>
.filter-section {
    padding: 2rem 0;
    background: var(--bg-light);
}

.media-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    height: 100%;
}

.media-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 25px rgba(0,0,0,0.15);
}

.media-thumbnail {
    position: relative;
    overflow: hidden;
}

.media-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.media-thumbnail:hover .media-overlay {
    opacity: 1;
}

.media-overlay-content {
    text-align: center;
}

.media-info {
    background: white;
}

.media-title {
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
}

.media-description {
    font-size: 0.9rem;
    line-height: 1.4;
}

.media-meta {
    font-size: 0.8rem;
}

#lightboxModal .modal-content {
    background: rgba(0,0,0,0.95) !important;
}

#lightboxModal .modal-body {
    padding: 1rem;
}

#lightboxModal img,
#lightboxModal video {
    max-height: 80vh;
    width: auto;
    border-radius: 8px;
}

.fade-in-up {
    animation: fadeInUp 0.6s ease forwards;
    opacity: 0;
    transform: translateY(30px);
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.video-placeholder,
.file-placeholder {
    border: 2px dashed #dee2e6;
    border-radius: 8px;
}

.media-item[data-type="video"] .media-card {
    border-left: 4px solid #dc3545;
}

.media-item[data-type="image"] .media-card {
    border-left: 4px solid #28a745;
}

.media-item[data-type="document"] .media-card {
    border-left: 4px solid #ffc107;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .media-card {
        margin-bottom: 1rem;
    }

    .filter-section .row {
        text-align: center;
    }

    .filter-section .col-md-6:first-child {
        margin-bottom: 1rem;
    }
}
</style>
@endpush
